{"version": 3, "file": "hammer.esm.js", "sources": ["../src/utils/assign.js", "../src/utils/utils-consts.js", "../src/utils/prefixed.js", "../src/browser.js", "../src/touchactionjs/get-touchaction-props.js", "../src/touchactionjs/touchaction-Consts.js", "../src/inputjs/input-consts.js", "../src/utils/each.js", "../src/utils/bool-or-fn.js", "../src/utils/in-str.js", "../src/touchactionjs/clean-touch-actions.js", "../src/touchactionjs/touchaction-constructor.js", "../src/utils/has-parent.js", "../src/inputjs/get-center.js", "../src/inputjs/simple-clone-input-data.js", "../src/inputjs/get-distance.js", "../src/inputjs/get-angle.js", "../src/inputjs/get-direction.js", "../src/inputjs/compute-delta-xy.js", "../src/inputjs/get-velocity.js", "../src/inputjs/get-scale.js", "../src/inputjs/get-rotation.js", "../src/inputjs/compute-interval-input-data.js", "../src/inputjs/compute-input-data.js", "../src/inputjs/input-handler.js", "../src/utils/split-str.js", "../src/utils/add-event-listeners.js", "../src/utils/remove-event-listeners.js", "../src/utils/get-window-for-element.js", "../src/inputjs/input-constructor.js", "../src/utils/in-array.js", "../src/input/pointerevent.js", "../src/utils/to-array.js", "../src/utils/unique-array.js", "../src/input/touch.js", "../src/input/mouse.js", "../src/input/touchmouse.js", "../src/inputjs/create-input-instance.js", "../src/utils/invoke-array-arg.js", "../src/recognizerjs/recognizer-consts.js", "../src/utils/unique-id.js", "../src/recognizerjs/get-recognizer-by-name-if-manager.js", "../src/recognizerjs/state-str.js", "../src/recognizerjs/recognizer-constructor.js", "../src/recognizers/tap.js", "../src/recognizers/attribute.js", "../src/recognizerjs/direction-str.js", "../src/recognizers/pan.js", "../src/recognizers/swipe.js", "../src/recognizers/pinch.js", "../src/recognizers/rotate.js", "../src/recognizers/press.js", "../src/defaults.js", "../src/manager.js", "../src/input/singletouch.js", "../src/utils/deprecate.js", "../src/utils/extend.js", "../src/utils/merge.js", "../src/utils/inherit.js", "../src/utils/bind-fn.js", "../src/hammer.js", "../src/index.js"], "sourcesContent": ["/**\n * @private\n * extend object.\n * means that properties in dest will be overwritten by the ones in src.\n * @param {Object} target\n * @param {...Object} objects_to_assign\n * @returns {Object} target\n */\nlet assign;\nif (typeof Object.assign !== 'function') {\n  assign = function assign(target) {\n    if (target === undefined || target === null) {\n      throw new TypeError('Cannot convert undefined or null to object');\n    }\n\n    let output = Object(target);\n    for (let index = 1; index < arguments.length; index++) {\n      const source = arguments[index];\n      if (source !== undefined && source !== null) {\n        for (const nextKey in source) {\n          if (source.hasOwnProperty(nextKey)) {\n            output[nextKey] = source[nextKey];\n          }\n        }\n      }\n    }\n    return output;\n  };\n} else {\n  assign = Object.assign;\n}\n\nexport default assign;", "\nconst VENDOR_PREFIXES = ['', 'webkit', 'Moz', 'MS', 'ms', 'o'];\nconst TEST_ELEMENT = typeof document === \"undefined\" ? {style: {}} : document.createElement('div');\n\nconst TYPE_FUNCTION = 'function';\n\nconst { round, abs } = Math;\nconst { now } = Date;\n\nexport {\n    VENDOR_PREFIXES,\n    TEST_ELEMENT,\n    TYPE_FUNCTION,\n    round,\n    abs,\n    now\n};\n", "import { VENDOR_PREFIXES } from './utils-consts';\n/**\n * @private\n * get the prefixed property\n * @param {Object} obj\n * @param {String} property\n * @returns {String|Undefined} prefixed\n */\nexport default function prefixed(obj, property) {\n  let prefix;\n  let prop;\n  let camelProp = property[0].toUpperCase() + property.slice(1);\n\n  let i = 0;\n  while (i < VENDOR_PREFIXES.length) {\n    prefix = VENDOR_PREFIXES[i];\n    prop = (prefix) ? prefix + camelProp : property;\n\n    if (prop in obj) {\n      return prop;\n    }\n    i++;\n  }\n  return undefined;\n}\n", "/* eslint-disable no-new-func, no-nested-ternary */\n\nlet win;\n\nif (typeof window === \"undefined\") {\n\t// window is undefined in node.js\n\twin = {};\n} else {\n\twin = window;\n}\n/* eslint-enable no-new-func, no-nested-ternary */\n\nexport {win as window};\n", "import prefixed from '../utils/prefixed';\nimport { TEST_ELEMENT } from '../utils/utils-consts';\nimport {window} from '../browser';\n\nexport const PREFIXED_TOUCH_ACTION = prefixed(TEST_ELEMENT.style, 'touchAction');\nexport const NATIVE_TOUCH_ACTION = PREFIXED_TOUCH_ACTION !== undefined;\n\nexport default function getTouchActionProps() {\n  if (!NATIVE_TOUCH_ACTION) {\n    return false;\n  }\n  let touchMap = {};\n  let cssSupports = window.CSS && window.CSS.supports;\n  ['auto', 'manipulation', 'pan-y', 'pan-x', 'pan-x pan-y', 'none'].forEach((val) => {\n\n    // If css.supports is not supported but there is native touch-action assume it supports\n    // all values. This is the case for IE 10 and 11.\n    return touchMap[val] = cssSupports ? window.CSS.supports('touch-action', val) : true;\n  });\n  return touchMap;\n}\n", "import getTouchActionProps from './get-touchaction-props';\n\n\n\n// magical touchAction value\nconst TOUCH_ACTION_COMPUTE = 'compute';\nconst TOUCH_ACTION_AUTO = 'auto';\nconst TOUCH_ACTION_MANIPULATION = 'manipulation'; // not implemented\nconst TOUCH_ACTION_NONE = 'none';\nconst TOUCH_ACTION_PAN_X = 'pan-x';\nconst TOUCH_ACTION_PAN_Y = 'pan-y';\nconst TOUCH_ACTION_MAP = getTouchActionProps();\n\nexport {\n  TOUCH_ACTION_AUTO,\n  TOUCH_ACTION_COMPUTE,\n  TOUCH_ACTION_MANIPULATION,\n  TOUCH_ACTION_NONE,\n  TOUCH_ACTION_PAN_X,\n  TOUCH_ACTION_PAN_Y,\n  TOUCH_ACTION_MAP\n};\n", "import prefixed from '../utils/prefixed';\nimport {window} from \"../browser\";\n\nconst MOBILE_REGEX = /mobile|tablet|ip(ad|hone|od)|android/i;\n\nconst SUPPORT_TOUCH = ('ontouchstart' in window);\nconst SUPPORT_POINTER_EVENTS = prefixed(window, 'PointerEvent') !== undefined;\nconst SUPPORT_ONLY_TOUCH = SUPPORT_TOUCH && MOBILE_REGEX.test(navigator.userAgent);\n\nconst INPUT_TYPE_TOUCH = 'touch';\nconst INPUT_TYPE_PEN = 'pen';\nconst INPUT_TYPE_MOUSE = 'mouse';\nconst INPUT_TYPE_KINECT = 'kinect';\n\nconst COMPUTE_INTERVAL = 25;\n\nconst INPUT_START = 1;\nconst INPUT_MOVE = 2;\nconst INPUT_END = 4;\nconst INPUT_CANCEL = 8;\n\nconst DIRECTION_NONE = 1;\nconst DIRECTION_LEFT = 2;\nconst DIRECTION_RIGHT = 4;\nconst DIRECTION_UP = 8;\nconst DIRECTION_DOWN = 16;\n\nconst DIRECTION_HORIZONTAL = DIRECTION_LEFT | DIRECTION_RIGHT;\nconst DIRECTION_VERTICAL = DIRECTION_UP | DIRECTION_DOWN;\nconst DIRECTION_ALL = DIRECTION_HORIZONTAL | DIRECTION_VERTICAL;\n\nconst PROPS_XY = ['x', 'y'];\nconst PROPS_CLIENT_XY = ['clientX', 'clientY'];\n\nexport {\n    MOBILE_REGEX,\n    SUPPORT_ONLY_TOUCH,\n    SUPPORT_POINTER_EVENTS,\n    SUPPORT_TOUCH,\n    INPUT_TYPE_KINECT,\n    INPUT_TYPE_MOUSE,\n    INPUT_TYPE_PEN,\n    INPUT_TYPE_TOUCH,\n    COMPUTE_INTERVAL,\n    INPUT_START,\n    INPUT_MOVE,\n    INPUT_END,\n    INPUT_CANCEL,\n    DIRECTION_NONE,\n    DIRECTION_LEFT,\n    DIRECTION_RIGHT,\n    DIRECTION_UP,\n    DIRECTION_DOWN,\n    DIRECTION_HORIZONTAL,\n    DIRECTION_VERTICAL,\n    DIRECTION_ALL,\n    PROPS_XY,\n    PROPS_CLIENT_XY\n};\n", "/**\n * @private\n * walk objects and arrays\n * @param {Object} obj\n * @param {Function} iterator\n * @param {Object} context\n */\nexport default function each(obj, iterator, context) {\n  let i;\n\n  if (!obj) {\n    return;\n  }\n\n  if (obj.forEach) {\n    obj.forEach(iterator, context);\n  } else if (obj.length !== undefined) {\n    i = 0;\n    while (i < obj.length) {\n      iterator.call(context, obj[i], i, obj);\n      i++;\n    }\n  } else {\n    for (i in obj) {\n      obj.hasOwnProperty(i) && iterator.call(context, obj[i], i, obj);\n    }\n  }\n}\n", "import { TYPE_FUNCTION } from './utils-consts';\n/**\n * @private\n * let a boolean value also be a function that must return a boolean\n * this first item in args will be used as the context\n * @param {Boolean|Function} val\n * @param {Array} [args]\n * @returns {Boolean}\n */\nexport default function boolOrFn(val, args) {\n  if (typeof val === TYPE_FUNCTION) {\n    return val.apply(args ? args[0] || undefined : undefined, args);\n  }\n  return val;\n}\n", "/**\n * @private\n * small indexOf wrapper\n * @param {String} str\n * @param {String} find\n * @returns {Boolean} found\n */\nexport default function inStr(str, find) {\n  return str.indexOf(find) > -1;\n}\n", "import inStr from '../utils/in-str';\nimport {\n    TOUCH_ACTION_NONE,\n    TOUCH_ACTION_PAN_X,\n    TOUCH_ACTION_PAN_Y,\n    TOUCH_ACTION_MANIPULATION,\n    TOUCH_ACTION_AUTO\n} from './touchaction-Consts';\n\n/**\n * @private\n * when the touchActions are collected they are not a valid value, so we need to clean things up. *\n * @param {String} actions\n * @returns {*}\n */\nexport default function cleanTouchActions(actions) {\n  // none\n  if (inStr(actions, TOUCH_ACTION_NONE)) {\n    return TOUCH_ACTION_NONE;\n  }\n\n  let hasPanX = inStr(actions, TOUCH_ACTION_PAN_X);\n  let hasPanY = inStr(actions, TOUCH_ACTION_PAN_Y);\n\n  // if both pan-x and pan-y are set (different recognizers\n  // for different directions, e.g. horizontal pan but vertical swipe?)\n  // we need none (as otherwise with pan-x pan-y combined none of these\n  // recognizers will work, since the browser would handle all panning\n  if (hasPanX && hasPanY) {\n    return TOUCH_ACTION_NONE;\n  }\n\n  // pan-x OR pan-y\n  if (hasPanX || hasPanY) {\n    return hasPanX ? TOUCH_ACTION_PAN_X : TOUCH_ACTION_PAN_Y;\n  }\n\n  // manipulation\n  if (inStr(actions, TOUCH_ACTION_MANIPULATION)) {\n    return TOUCH_ACTION_MANIPULATION;\n  }\n\n  return TOUCH_ACTION_AUTO;\n}\n", "import {\n    TOUCH_ACTION_COMPUTE,\n    TOUCH_ACTION_MAP,\n    TOUCH_ACTION_NONE,\n    TOUCH_ACTION_PAN_X,\n    TOUCH_ACTION_PAN_Y\n} from './touchaction-Consts';\nimport {\n  NATIVE_TOUCH_ACTION,\n  PREFIXED_TOUCH_ACTION,\n} from \"./get-touchaction-props\";\nimport {\n    DIRECTION_VERTICAL,\n    DIRECTION_HORIZONTAL\n} from '../inputjs/input-consts';\nimport each from '../utils/each';\nimport boolOrFn from '../utils/bool-or-fn';\nimport inStr from '../utils/in-str';\nimport cleanTouchActions from './clean-touch-actions';\n\n/**\n * @private\n * Touch Action\n * sets the touchAction property or uses the js alternative\n * @param {Manager} manager\n * @param {String} value\n * @constructor\n */\nexport default class TouchAction {\n  constructor(manager, value) {\n    this.manager = manager;\n    this.set(value);\n  }\n\n  /**\n   * @private\n   * set the touchAction value on the element or enable the polyfill\n   * @param {String} value\n   */\n  set(value) {\n    // find out the touch-action by the event handlers\n    if (value === TOUCH_ACTION_COMPUTE) {\n      value = this.compute();\n    }\n\n    if (NATIVE_TOUCH_ACTION && this.manager.element.style && TOUCH_ACTION_MAP[value]) {\n      this.manager.element.style[PREFIXED_TOUCH_ACTION] = value;\n    }\n    this.actions = value.toLowerCase().trim();\n  }\n\n  /**\n   * @private\n   * just re-set the touchAction value\n   */\n  update() {\n    this.set(this.manager.options.touchAction);\n  }\n\n  /**\n   * @private\n   * compute the value for the touchAction property based on the recognizer's settings\n   * @returns {String} value\n   */\n  compute() {\n    let actions = [];\n    each(this.manager.recognizers, (recognizer) => {\n      if (boolOrFn(recognizer.options.enable, [recognizer])) {\n        actions = actions.concat(recognizer.getTouchAction());\n      }\n    });\n    return cleanTouchActions(actions.join(' '));\n  }\n\n  /**\n   * @private\n   * this method is called on each input cycle and provides the preventing of the browser behavior\n   * @param {Object} input\n   */\n  preventDefaults(input) {\n    let { srcEvent } = input;\n    let direction = input.offsetDirection;\n\n    // if the touch action did prevented once this session\n    if (this.manager.session.prevented) {\n      srcEvent.preventDefault();\n      return;\n    }\n\n    let { actions } = this;\n    let hasNone = inStr(actions, TOUCH_ACTION_NONE) && !TOUCH_ACTION_MAP[TOUCH_ACTION_NONE];\n    let hasPanY = inStr(actions, TOUCH_ACTION_PAN_Y) && !TOUCH_ACTION_MAP[TOUCH_ACTION_PAN_Y];\n    let hasPanX = inStr(actions, TOUCH_ACTION_PAN_X) && !TOUCH_ACTION_MAP[TOUCH_ACTION_PAN_X];\n\n    if (hasNone) {\n      // do not prevent defaults if this is a tap gesture\n      let isTapPointer = input.pointers.length === 1;\n      let isTapMovement = input.distance < 2;\n      let isTapTouchTime = input.deltaTime < 250;\n\n      if (isTapPointer && isTapMovement && isTapTouchTime) {\n        return;\n      }\n    }\n\n    if (hasPanX && hasPanY) {\n      // `pan-x pan-y` means browser handles all scrolling/panning, do not prevent\n      return;\n    }\n\n    if (hasNone ||\n        (hasPanY && direction & DIRECTION_HORIZONTAL) ||\n        (hasPanX && direction & DIRECTION_VERTICAL)) {\n      return this.preventSrc(srcEvent);\n    }\n  }\n\n  /**\n   * @private\n   * call preventDefault to prevent the browser's default behavior (scrolling in most cases)\n   * @param {Object} srcEvent\n   */\n  preventSrc(srcEvent) {\n    this.manager.session.prevented = true;\n    srcEvent.preventDefault();\n  }\n}\n", "/**\n * @private\n * find if a node is in the given parent\n * @method hasParent\n * @param {HTMLElement} node\n * @param {HTMLElement} parent\n * @return {<PERSON>olean} found\n */\nexport default function hasParent(node, parent) {\n  while (node) {\n    if (node === parent) {\n      return true;\n    }\n    node = node.parentNode;\n  }\n  return false;\n}\n", "import { round } from '../utils/utils-consts';\n\n/**\n * @private\n * get the center of all the pointers\n * @param {Array} pointers\n * @return {Object} center contains `x` and `y` properties\n */\nexport default function getCenter(pointers) {\n  let pointersLength = pointers.length;\n\n  // no need to loop when only one touch\n  if (pointersLength === 1) {\n    return {\n      x: round(pointers[0].clientX),\n      y: round(pointers[0].clientY)\n    };\n  }\n\n  let x = 0;\n  let y = 0;\n  let i = 0;\n  while (i < pointersLength) {\n    x += pointers[i].clientX;\n    y += pointers[i].clientY;\n    i++;\n  }\n\n  return {\n    x: round(x / pointersLength),\n    y: round(y / pointersLength)\n  };\n}\n", "import { now,round } from '../utils/utils-consts';\nimport getCenter from './get-center';\n\n/**\n * @private\n * create a simple clone from the input used for storage of firstInput and firstMultiple\n * @param {Object} input\n * @returns {Object} clonedInputData\n */\nexport default function simpleCloneInputData(input) {\n  // make a simple copy of the pointers because we will get a reference if we don't\n  // we only need clientXY for the calculations\n  let pointers = [];\n  let i = 0;\n  while (i < input.pointers.length) {\n    pointers[i] = {\n      clientX: round(input.pointers[i].clientX),\n      clientY: round(input.pointers[i].clientY)\n    };\n    i++;\n  }\n\n  return {\n    timeStamp: now(),\n    pointers,\n    center: getCenter(pointers),\n    deltaX: input.deltaX,\n    deltaY: input.deltaY\n  };\n}\n", "import { PROPS_XY } from './input-consts';\n\n/**\n * @private\n * calculate the absolute distance between two points\n * @param {Object} p1 {x, y}\n * @param {Object} p2 {x, y}\n * @param {Array} [props] containing x and y keys\n * @return {Number} distance\n */\nexport default function getDistance(p1, p2, props) {\n  if (!props) {\n    props = PROPS_XY;\n  }\n  let x = p2[props[0]] - p1[props[0]];\n  let y = p2[props[1]] - p1[props[1]];\n\n  return Math.sqrt((x * x) + (y * y));\n}\n", "import { PROPS_XY } from './input-consts';\n\n/**\n * @private\n * calculate the angle between two coordinates\n * @param {Object} p1\n * @param {Object} p2\n * @param {Array} [props] containing x and y keys\n * @return {Number} angle\n */\nexport default function getAngle(p1, p2, props) {\n  if (!props) {\n    props = PROPS_XY;\n  }\n  let x = p2[props[0]] - p1[props[0]];\n  let y = p2[props[1]] - p1[props[1]];\n  return Math.atan2(y, x) * 180 / Math.PI;\n}\n", "import { abs } from '../utils/utils-consts';\nimport { DIRECTION_NONE,DIRECTION_LEFT,DIRECTION_RIGHT,DIRECTION_UP,DIRECTION_DOWN } from './input-consts';\n\n/**\n * @private\n * get the direction between two points\n * @param {Number} x\n * @param {Number} y\n * @return {Number} direction\n */\nexport default function getDirection(x, y) {\n  if (x === y) {\n    return DIRECTION_NONE;\n  }\n\n  if (abs(x) >= abs(y)) {\n    return x < 0 ? DIRECTION_LEFT : DIRECTION_RIGHT;\n  }\n  return y < 0 ? DIRECTION_UP : DIRECTION_DOWN;\n}\n", "import { INPUT_START, INPUT_END } from './input-consts';\n\nexport default function computeDeltaXY(session, input) {\n  let { center } = input;\n  // let { offsetDelta:offset = {}, prevDelta = {}, prevInput = {} } = session;\n  // jscs throwing error on defalut destructured values and without defaults tests fail\n  let offset = session.offsetDelta || {};\n  let prevDelta = session.prevDelta || {};\n  let prevInput = session.prevInput || {};\n\n  if (input.eventType === INPUT_START || prevInput.eventType === INPUT_END) {\n    prevDelta = session.prevDelta = {\n      x: prevInput.deltaX || 0,\n      y: prevInput.deltaY || 0\n    };\n\n    offset = session.offsetDelta = {\n      x: center.x,\n      y: center.y\n    };\n  }\n\n  input.deltaX = prevDelta.x + (center.x - offset.x);\n  input.deltaY = prevDelta.y + (center.y - offset.y);\n}\n", "/**\n * @private\n * calculate the velocity between two points. unit is in px per ms.\n * @param {Number} deltaTime\n * @param {Number} x\n * @param {Number} y\n * @return {Object} velocity `x` and `y`\n */\nexport default function getVelocity(deltaTime, x, y) {\n  return {\n    x: x / deltaTime || 0,\n    y: y / deltaTime || 0\n  };\n}\n", "import { PROPS_CLIENT_XY } from './input-consts';\nimport getDistance from './get-distance';\n/**\n * @private\n * calculate the scale factor between two pointersets\n * no scale is 1, and goes down to 0 when pinched together, and bigger when pinched out\n * @param {Array} start array of pointers\n * @param {Array} end array of pointers\n * @return {Number} scale\n */\nexport default function getScale(start, end) {\n  return getDistance(end[0], end[1], PROPS_CLIENT_XY) / getDistance(start[0], start[1], PROPS_CLIENT_XY);\n}\n", "import getAngle from './get-angle';\nimport { PROPS_CLIENT_XY } from './input-consts';\n\n/**\n * @private\n * calculate the rotation degrees between two pointersets\n * @param {Array} start array of pointers\n * @param {Array} end array of pointers\n * @return {Number} rotation\n */\nexport default function getRotation(start, end) {\n  return getAngle(end[1], end[0], PROPS_CLIENT_XY) + getAngle(start[1], start[0], PROPS_CLIENT_XY);\n}\n", "import { INPUT_CANCEL,COMPUTE_INTERVAL } from './input-consts';\nimport { abs } from '../utils/utils-consts';\nimport getVelocity from './get-velocity';\nimport getDirection from './get-direction';\n\n/**\n * @private\n * velocity is calculated every x ms\n * @param {Object} session\n * @param {Object} input\n */\nexport default function computeIntervalInputData(session, input) {\n  let last = session.lastInterval || input;\n  let deltaTime = input.timeStamp - last.timeStamp;\n  let velocity;\n  let velocityX;\n  let velocityY;\n  let direction;\n\n  if (input.eventType !== INPUT_CANCEL && (deltaTime > COMPUTE_INTERVAL || last.velocity === undefined)) {\n    let deltaX = input.deltaX - last.deltaX;\n    let deltaY = input.deltaY - last.deltaY;\n\n    let v = getVelocity(deltaTime, deltaX, deltaY);\n    velocityX = v.x;\n    velocityY = v.y;\n    velocity = (abs(v.x) > abs(v.y)) ? v.x : v.y;\n    direction = getDirection(deltaX, deltaY);\n\n    session.lastInterval = input;\n  } else {\n    // use latest velocity info if it doesn't overtake a minimum period\n    velocity = last.velocity;\n    velocityX = last.velocityX;\n    velocityY = last.velocityY;\n    direction = last.direction;\n  }\n\n  input.velocity = velocity;\n  input.velocityX = velocityX;\n  input.velocityY = velocityY;\n  input.direction = direction;\n}\n", "import { now } from '../utils/utils-consts';\nimport { abs } from '../utils/utils-consts';\nimport hasParent from '../utils/has-parent';\nimport simpleCloneInputData from './simple-clone-input-data';\nimport getCenter from './get-center';\nimport getDistance from './get-distance';\nimport getAngle from './get-angle';\nimport getDirection from './get-direction';\nimport computeDeltaXY from './compute-delta-xy';\nimport getVelocity from './get-velocity';\nimport getScale from './get-scale';\nimport getRotation from './get-rotation';\nimport computeIntervalInputData from './compute-interval-input-data';\n\n/**\n* @private\n * extend the data with some usable properties like scale, rotate, velocity etc\n * @param {Object} manager\n * @param {Object} input\n */\nexport default function computeInputData(manager, input) {\n  let { session } = manager;\n  let { pointers } = input;\n  let { length:pointersLength } = pointers;\n\n  // store the first input to calculate the distance and direction\n  if (!session.firstInput) {\n    session.firstInput = simpleCloneInputData(input);\n  }\n\n  // to compute scale and rotation we need to store the multiple touches\n  if (pointersLength > 1 && !session.firstMultiple) {\n    session.firstMultiple = simpleCloneInputData(input);\n  } else if (pointersLength === 1) {\n    session.firstMultiple = false;\n  }\n\n  let { firstInput, firstMultiple } = session;\n  let offsetCenter = firstMultiple ? firstMultiple.center : firstInput.center;\n\n  let center = input.center = getCenter(pointers);\n  input.timeStamp = now();\n  input.deltaTime = input.timeStamp - firstInput.timeStamp;\n\n  input.angle = getAngle(offsetCenter, center);\n  input.distance = getDistance(offsetCenter, center);\n\n  computeDeltaXY(session, input);\n  input.offsetDirection = getDirection(input.deltaX, input.deltaY);\n\n  let overallVelocity = getVelocity(input.deltaTime, input.deltaX, input.deltaY);\n  input.overallVelocityX = overallVelocity.x;\n  input.overallVelocityY = overallVelocity.y;\n  input.overallVelocity = (abs(overallVelocity.x) > abs(overallVelocity.y)) ? overallVelocity.x : overallVelocity.y;\n\n  input.scale = firstMultiple ? getScale(firstMultiple.pointers, pointers) : 1;\n  input.rotation = firstMultiple ? getRotation(firstMultiple.pointers, pointers) : 0;\n\n  input.maxPointers = !session.prevInput ? input.pointers.length : ((input.pointers.length >\n  session.prevInput.maxPointers) ? input.pointers.length : session.prevInput.maxPointers);\n\n  computeIntervalInputData(session, input);\n\n  // find the correct target\n  let target = manager.element;\n  const srcEvent = input.srcEvent;\n  let srcEventTarget;\n\n  if (srcEvent.composedPath) {\n    srcEventTarget = srcEvent.composedPath()[0];\n  } else if (srcEvent.path) {\n    srcEventTarget = srcEvent.path[0];\n  } else {\n    srcEventTarget = srcEvent.target;\n  }\n\n  if (hasParent(srcEventTarget, target)) {\n    target = srcEventTarget;\n  }\n  input.target = target;\n}\n", "import { INPUT_START,INPUT_END,INPUT_CANCEL } from './input-consts';\nimport computeInputData from './compute-input-data';\n\n/**\n * @private\n * handle input events\n * @param {Manager} manager\n * @param {String} eventType\n * @param {Object} input\n */\nexport default function inputHandler(manager, eventType, input) {\n  let pointersLen = input.pointers.length;\n  let changedPointersLen = input.changedPointers.length;\n  let isFirst = (eventType & INPUT_START && (pointersLen - changedPointersLen === 0));\n  let isFinal = (eventType & (INPUT_END | INPUT_CANCEL) && (pointersLen - changedPointersLen === 0));\n\n  input.isFirst = !!isFirst;\n  input.isFinal = !!isFinal;\n\n  if (isFirst) {\n    manager.session = {};\n  }\n\n  // source event is the normalized value of the domEvents\n  // like 'touchstart, mouseup, pointerdown'\n  input.eventType = eventType;\n\n  // compute scale, rotation etc\n  computeInputData(manager, input);\n\n  // emit secret event\n  manager.emit('hammer.input', input);\n\n  manager.recognize(input);\n  manager.session.prevInput = input;\n}\n", "/**\n * @private\n * split string on whitespace\n * @param {String} str\n * @returns {Array} words\n */\n\nexport default function splitStr(str) {\n  return str.trim().split(/\\s+/g);\n}\n", "import each from './each';\nimport splitStr from './split-str';\n/**\n * @private\n * addEventListener with multiple events at once\n * @param {EventTarget} target\n * @param {String} types\n * @param {Function} handler\n */\nexport default function addEventListeners(target, types, handler) {\n  each(splitStr(types), (type) => {\n    target.addEventListener(type, handler, false);\n  });\n}\n", "import each from './each';\nimport splitStr from './split-str';\n/**\n * @private\n * removeEventListener with multiple events at once\n * @param {EventTarget} target\n * @param {String} types\n * @param {Function} handler\n */\nexport default function removeEventListeners(target, types, handler) {\n  each(splitStr(types), (type) => {\n    target.removeEventListener(type, handler, false);\n  });\n}\n", "/**\n * @private\n * get the window object of an element\n * @param {HTMLElement} element\n * @returns {DocumentView|Window}\n */\nexport default function getWindowForElement(element) {\n  let doc = element.ownerDocument || element;\n  return (doc.defaultView || doc.parentWindow || window);\n}\n", "import boolOrFn from '../utils/bool-or-fn';\nimport addEventListeners from '../utils/add-event-listeners';\nimport removeEventListeners from '../utils/remove-event-listeners';\nimport getWindowForElement from '../utils/get-window-for-element';\n\n/**\n * @private\n * create new input type manager\n * @param {Manager} manager\n * @param {Function} callback\n * @returns {Input}\n * @constructor\n */\nexport default class Input {\n  constructor(manager, callback) {\n    let self = this;\n    this.manager = manager;\n    this.callback = callback;\n    this.element = manager.element;\n    this.target = manager.options.inputTarget;\n\n    // smaller wrapper around the handler, for the scope and the enabled state of the manager,\n    // so when disabled the input events are completely bypassed.\n    this.domHandler = function(ev) {\n      if (boolOrFn(manager.options.enable, [manager])) {\n        self.handler(ev);\n      }\n    };\n\n    this.init();\n\n  }\n  /**\n   * @private\n   * should handle the inputEvent data and trigger the callback\n   * @virtual\n   */\n  handler() { }\n\n  /**\n   * @private\n   * bind the events\n   */\n  init() {\n    this.evEl && addEventListeners(this.element, this.evEl, this.domHandler);\n    this.evTarget && addEventListeners(this.target, this.evTarget, this.domHandler);\n    this.evWin && addEventListeners(getWindowForElement(this.element), this.evWin, this.domHandler);\n  }\n\n  /**\n   * @private\n   * unbind the events\n   */\n  destroy() {\n    this.evEl && removeEventListeners(this.element, this.evEl, this.domHandler);\n    this.evTarget && removeEventListeners(this.target, this.evTarget, this.domHandler);\n    this.evWin && removeEventListeners(getWindowForElement(this.element), this.evWin, this.domHandler);\n  }\n}\n", "/**\n * @private\n * find if a array contains the object using indexOf or a simple polyFill\n * @param {Array} src\n * @param {String} find\n * @param {String} [findBy<PERSON><PERSON>]\n * @return {Boolean|Number} false when not found, or the index\n */\nexport default function inArray(src, find, findByKey) {\n  if (src.indexOf && !findByKey) {\n    return src.indexOf(find);\n  } else {\n    let i = 0;\n    while (i < src.length) {\n      if ((findByKey && src[i][findByKey] == find) || (!findByKey && src[i] === find)) {// do not use === here, test fails\n        return i;\n      }\n      i++;\n    }\n    return -1;\n  }\n}\n", "import {\n    INPUT_START,\n    INPUT_END,\n    INPUT_CANCEL,\n    INPUT_MOVE,\n    INPUT_TYPE_TOUCH,\n    INPUT_TYPE_MOUSE,\n    INPUT_TYPE_PEN,\n    INPUT_TYPE_KINECT\n} from '../inputjs/input-consts';\nimport {window} from \"../browser\";\nimport Input from '../inputjs/input-constructor';\nimport inArray from '../utils/in-array';\n\nconst POINTER_INPUT_MAP = {\n  pointerdown: INPUT_START,\n  pointermove: INPUT_MOVE,\n  pointerup: INPUT_END,\n  pointercancel: INPUT_CANCEL,\n  pointerout: INPUT_CANCEL\n};\n\n// in IE10 the pointer types is defined as an enum\nconst IE10_POINTER_TYPE_ENUM = {\n  2: INPUT_TYPE_TOUCH,\n  3: INPUT_TYPE_PEN,\n  4: INPUT_TYPE_MOUSE,\n  5: INPUT_TYPE_KINECT // see https://twitter.com/jacobrossi/status/480596438489890816\n};\n\nlet POINTER_ELEMENT_EVENTS = 'pointerdown';\nlet POINTER_WINDOW_EVENTS = 'pointermove pointerup pointercancel';\n\n// IE10 has prefixed support, and case-sensitive\nif (window.MSPointerEvent && !window.PointerEvent) {\n  POINTER_ELEMENT_EVENTS = 'MSPointerDown';\n  POINTER_WINDOW_EVENTS = 'MSPointerMove MSPointerUp MSPointerCancel';\n}\n\n/**\n * @private\n * Pointer events input\n * @constructor\n * @extends Input\n */\nexport default class PointerEventInput extends Input {\n  constructor() {\n    var proto = PointerEventInput.prototype;\n\n    proto.evEl = POINTER_ELEMENT_EVENTS;\n    proto.evWin = POINTER_WINDOW_EVENTS;\n    super(...arguments);\n    this.store = (this.manager.session.pointerEvents = []);\n  }\n\n  /**\n   * @private\n   * handle mouse events\n   * @param {Object} ev\n   */\n  handler(ev) {\n    let { store } = this;\n    let removePointer = false;\n\n    let eventTypeNormalized = ev.type.toLowerCase().replace('ms', '');\n    let eventType = POINTER_INPUT_MAP[eventTypeNormalized];\n    let pointerType = IE10_POINTER_TYPE_ENUM[ev.pointerType] || ev.pointerType;\n\n    let isTouch = (pointerType === INPUT_TYPE_TOUCH);\n\n    // get index of the event in the store\n    let storeIndex = inArray(store, ev.pointerId, 'pointerId');\n\n    // start and mouse must be down\n    if (eventType & INPUT_START && (ev.button === 0 || isTouch)) {\n      if (storeIndex < 0) {\n        store.push(ev);\n        storeIndex = store.length - 1;\n      }\n    } else if (eventType & (INPUT_END | INPUT_CANCEL)) {\n      removePointer = true;\n    }\n\n    // it not found, so the pointer hasn't been down (so it's probably a hover)\n    if (storeIndex < 0) {\n      return;\n    }\n\n    // update the event in the store\n    store[storeIndex] = ev;\n\n    this.callback(this.manager, eventType, {\n      pointers: store,\n      changedPointers: [ev],\n      pointerType,\n      srcEvent: ev\n    });\n\n    if (removePointer) {\n      // remove from the store\n      store.splice(storeIndex, 1);\n    }\n  }\n}\n", "/**\n * @private\n * convert array-like objects to real arrays\n * @param {Object} obj\n * @returns {Array}\n */\nexport default function toArray(obj) {\n  return Array.prototype.slice.call(obj, 0);\n}\n", "import inArray from './in-array';\n\n/**\n * @private\n * unique array with objects based on a key (like 'id') or just by the array's value\n * @param {Array} src [{id:1},{id:2},{id:1}]\n * @param {String} [key]\n * @param {Boolean} [sort=False]\n * @returns {Array} [{id:1},{id:2}]\n */\nexport default function uniqueArray(src, key, sort) {\n  let results = [];\n  let values = [];\n  let i = 0;\n\n  while (i < src.length) {\n    let val = key ? src[i][key] : src[i];\n    if (inArray(values, val) < 0) {\n      results.push(src[i]);\n    }\n    values[i] = val;\n    i++;\n  }\n\n  if (sort) {\n    if (!key) {\n      results = results.sort();\n    } else {\n      results = results.sort((a, b) => {\n        return a[key] > b[key];\n      });\n    }\n  }\n\n  return results;\n}\n", "import {\n  INPUT_START,\n  INPUT_MOVE,\n  INPUT_END,\n  INPUT_CANCEL,\n  INPUT_TYPE_TOUCH\n} from '../inputjs/input-consts';\nimport Input from '../inputjs/input-constructor';\nimport toArray from '../utils/to-array';\nimport hasParent from '../utils/has-parent';\nimport uniqueArray from '../utils/unique-array';\n\nconst TOUCH_INPUT_MAP = {\n  touchstart: INPUT_START,\n  touchmove: INPUT_MOVE,\n  touchend: INPUT_END,\n  touchcancel: INPUT_CANCEL\n};\n\nconst TOUCH_TARGET_EVENTS = 'touchstart touchmove touchend touchcancel';\n\n/**\n * @private\n * Multi-user touch events input\n * @constructor\n * @extends Input\n */\nexport default class TouchInput extends Input {\n  constructor() {\n    TouchInput.prototype.evTarget = TOUCH_TARGET_EVENTS;\n    super(...arguments);\n    this.targetIds = {};\n    // this.evTarget = TOUCH_TARGET_EVENTS;\n  }\n  handler(ev) {\n    let type = TOUCH_INPUT_MAP[ev.type];\n    let touches = getTouches.call(this, ev, type);\n    if (!touches) {\n      return;\n    }\n\n    this.callback(this.manager, type, {\n      pointers: touches[0],\n      changedPointers: touches[1],\n      pointerType: INPUT_TYPE_TOUCH,\n      srcEvent: ev\n    });\n  }\n}\n\n/**\n * @private\n * @this {TouchInput}\n * @param {Object} ev\n * @param {Number} type flag\n * @returns {undefined|Array} [all, changed]\n */\nfunction getTouches(ev, type) {\n  let allTouches = toArray(ev.touches);\n  let { targetIds } = this;\n\n  // when there is only one touch, the process can be simplified\n  if (type & (INPUT_START | INPUT_MOVE) && allTouches.length === 1) {\n    targetIds[allTouches[0].identifier] = true;\n    return [allTouches, allTouches];\n  }\n\n  let i;\n  let targetTouches;\n  let changedTouches = toArray(ev.changedTouches);\n  let changedTargetTouches = [];\n  let { target } = this;\n\n  // get target touches from touches\n  targetTouches = allTouches.filter((touch) => {\n    return hasParent(touch.target, target);\n  });\n\n  // collect touches\n  if (type === INPUT_START) {\n    i = 0;\n    while (i < targetTouches.length) {\n      targetIds[targetTouches[i].identifier] = true;\n      i++;\n    }\n  }\n\n  // filter changed touches to only contain touches that exist in the collected target ids\n  i = 0;\n  while (i < changedTouches.length) {\n    if (targetIds[changedTouches[i].identifier]) {\n      changedTargetTouches.push(changedTouches[i]);\n    }\n\n    // cleanup removed touches\n    if (type & (INPUT_END | INPUT_CANCEL)) {\n      delete targetIds[changedTouches[i].identifier];\n    }\n    i++;\n  }\n\n  if (!changedTargetTouches.length) {\n    return;\n  }\n\n  return [\n    // merge targetTouches with changedTargetTouches so it contains ALL touches, including 'end' and 'cancel'\n    uniqueArray(targetTouches.concat(changedTargetTouches), 'identifier', true),\n    changedTargetTouches\n  ];\n}\n", "import {\n    INPUT_START,\n    INPUT_MOVE,\n    INPUT_END,\n    INPUT_TYPE_MOUSE\n} from '../inputjs/input-consts';\nimport Input from '../inputjs/input-constructor';\n\nconst MOUSE_INPUT_MAP = {\n  mousedown: INPUT_START,\n  mousemove: INPUT_MOVE,\n  mouseup: INPUT_END\n};\n\nconst MOUSE_ELEMENT_EVENTS = 'mousedown';\nconst MOUSE_WINDOW_EVENTS = 'mousemove mouseup';\n\n/**\n * @private\n * Mouse events input\n * @constructor\n * @extends Input\n */\nexport default class MouseInput extends Input {\n  constructor() {\n    var proto = MouseInput.prototype;\n    proto.evEl = MOUSE_ELEMENT_EVENTS;\n    proto.evWin = MOUSE_WINDOW_EVENTS;\n\n    super(...arguments);\n    this.pressed = false; // mousedown state\n  }\n\n  /**\n   * @private\n   * handle mouse events\n   * @param {Object} ev\n   */\n  handler(ev) {\n    let eventType = MOUSE_INPUT_MAP[ev.type];\n\n    // on start we want to have the left mouse button down\n    if (eventType & INPUT_START && ev.button === 0) {\n      this.pressed = true;\n    }\n\n    if (eventType & INPUT_MOVE && ev.which !== 1) {\n      eventType = INPUT_END;\n    }\n\n    // mouse must be down\n    if (!this.pressed) {\n      return;\n    }\n\n    if (eventType & INPUT_END) {\n      this.pressed = false;\n    }\n\n    this.callback(this.manager, eventType, {\n      pointers: [ev],\n      changedPointers: [ev],\n      pointerType: INPUT_TYPE_MOUSE,\n      srcEvent: ev\n    });\n  }\n}\n", "import Input from \"../inputjs/input-constructor\";\nimport TouchInput from \"./touch\";\nimport MouseInput from \"./mouse\";\nimport {\n\tINPUT_START,\n\tINPUT_END,\n\tINPUT_CANCEL,\n\tINPUT_TYPE_TOUCH,\n\tINPUT_TYPE_MOUSE,\n} from \"../inputjs/input-consts\";\n\n/**\n * @private\n * Combined touch and mouse input\n *\n * Touch has a higher priority then mouse, and while touching no mouse events are allowed.\n * This because touch devices also emit mouse events while doing a touch.\n *\n * @constructor\n * @extends Input\n */\n\nconst DEDUP_TIMEOUT = 2500;\nconst DEDUP_DISTANCE = 25;\n\nfunction setLastTouch(eventData) {\n\tconst { changedPointers: [touch] } = eventData;\n\n\tif (touch.identifier === this.primaryTouch) {\n\t\tconst lastTouch = { x: touch.clientX, y: touch.clientY };\n\t\tconst lts = this.lastTouches;\n\n\t\tthis.lastTouches.push(lastTouch);\n\n\n\t\tconst removeLastTouch = function() {\n\t\t\tconst i = lts.indexOf(lastTouch);\n\n\t\t\tif (i > -1) {\n\t\t\t\tlts.splice(i, 1);\n\t\t\t}\n\t\t};\n\n\t\tsetTimeout(removeLastTouch, DEDUP_TIMEOUT);\n\t}\n}\n\n\nfunction recordTouches(eventType, eventData) {\n\tif (eventType & INPUT_START) {\n\t\tthis.primaryTouch = eventData.changedPointers[0].identifier;\n\t\tsetLastTouch.call(this, eventData);\n\t} else if (eventType & (INPUT_END | INPUT_CANCEL)) {\n\t\tsetLastTouch.call(this, eventData);\n\t}\n}\nfunction isSyntheticEvent(eventData) {\n\tconst x = eventData.srcEvent.clientX;\n\tconst y = eventData.srcEvent.clientY;\n\n\tfor (let i = 0; i < this.lastTouches.length; i++) {\n\t\tconst t = this.lastTouches[i];\n\t\tconst dx = Math.abs(x - t.x);\n\t\tconst dy = Math.abs(y - t.y);\n\n\t\tif (dx <= DEDUP_DISTANCE && dy <= DEDUP_DISTANCE) {\n\t\t\treturn true;\n\t\t}\n\t}\n\treturn false;\n}\n\n\nexport default class TouchMouseInput extends Input {\n\tconstructor(manager, callback) {\n\t\tsuper(manager, callback);\n\n\t\tthis.touch = new TouchInput(this.manager, this.handler);\n\t\tthis.mouse = new MouseInput(this.manager, this.handler);\n\t\tthis.primaryTouch = null;\n\t\tthis.lastTouches = [];\n\t}\n\n\t/**\n\t * @private\n\t * handle mouse and touch events\n\t * @param {Hammer} manager\n\t * @param {String} inputEvent\n\t * @param {Object} inputData\n\t */\n\thandler = (manager, inputEvent, inputData) => {\n\t\tconst isTouch = (inputData.pointerType === INPUT_TYPE_TOUCH);\n\t\tconst isMouse = (inputData.pointerType === INPUT_TYPE_MOUSE);\n\n\t\tif (isMouse && inputData.sourceCapabilities && inputData.sourceCapabilities.firesTouchEvents) {\n\t\t\treturn;\n\t\t}\n\n\t\t// when we're in a touch event, record touches to  de-dupe synthetic mouse event\n\t\tif (isTouch) {\n\t\t\trecordTouches.call(this, inputEvent, inputData);\n\t\t} else if (isMouse && isSyntheticEvent.call(this, inputData)) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.callback(manager, inputEvent, inputData);\n\t}\n\n\t/**\n\t * @private\n\t * remove the event listeners\n\t */\n\tdestroy() {\n\t\tthis.touch.destroy();\n\t\tthis.mouse.destroy();\n\t}\n}\n", "import { SUPPORT_POINTER_EVENTS,SUPPORT_ONLY_TOUCH,SUPPORT_TOUCH } from './input-consts';\nimport inputHandler from './input-handler';\nimport PointerEventInput from '../input/pointerevent';\nimport TouchInput from '../input/touch';\nimport MouseInput from '../input/mouse';\nimport TouchMouseInput from '../input/touchmouse';\n\n/**\n * @private\n * create new input type manager\n * called by the Manager constructor\n * @param {Hammer} manager\n * @returns {Input}\n */\nexport default function createInputInstance(manager) {\n  let Type;\n  // let inputClass = manager.options.inputClass;\n  let { options:{ inputClass } } = manager;\n  if (inputClass) {\n    Type = inputClass;\n  } else if (SUPPORT_POINTER_EVENTS) {\n    Type = PointerEventInput;\n  } else if (SUPPORT_ONLY_TOUCH) {\n    Type = TouchInput;\n  } else if (!SUPPORT_TOUCH) {\n    Type = MouseInput;\n  } else {\n    Type = TouchMouseInput;\n  }\n  return new (Type)(manager, inputHandler);\n}\n", "import each from './each';\n/**\n * @private\n * if the argument is an array, we want to execute the fn on each entry\n * if it aint an array we don't want to do a thing.\n * this is used by all the methods that accept a single and array argument.\n * @param {*|Array} arg\n * @param {String} fn\n * @param {Object} [context]\n * @returns {Boolean}\n */\nexport default function invokeArrayArg(arg, fn, context) {\n  if (Array.isArray(arg)) {\n    each(arg, context[fn], context);\n    return true;\n  }\n  return false;\n}\n", "const STATE_POSSIBLE = 1;\nconst STATE_BEGAN = 2;\nconst STATE_CHANGED = 4;\nconst STATE_ENDED = 8;\nconst STATE_RECOGNIZED = STATE_ENDED;\nconst STATE_CANCELLED = 16;\nconst STATE_FAILED = 32;\n\nexport {\n    STATE_POSSIBLE,\n    STATE_BEGAN,\n    STATE_CHANGED,\n    STATE_ENDED,\n    STATE_RECOGNIZED,\n    STATE_CANCELLED,\n    STATE_FAILED\n};\n", "/**\n * @private\n * get a unique id\n * @returns {number} uniqueId\n */\nlet _uniqueId = 1;\nexport default function uniqueId() {\n  return _uniqueId++;\n}\n", "/**\n * @private\n * get a recognizer by name if it is bound to a manager\n * @param {Recognizer|String} otherRecognizer\n * @param {Recognizer} recognizer\n * @returns {Recognizer}\n */\nexport default function getRecognizerByNameIfManager(otherRecognizer, recognizer) {\n  let { manager } = recognizer;\n  if (manager) {\n    return manager.get(otherRecognizer);\n  }\n  return otherRecognizer;\n}\n", "import {\n    STATE_CANCELLED,\n    STATE_ENDED,\n    STATE_CHANGED,\n    STATE_BEGAN\n} from './recognizer-consts';\n\n/**\n * @private\n * get a usable string, used as event postfix\n * @param {constant} state\n * @returns {String} state\n */\nexport default function stateStr(state) {\n  if (state & STATE_CANCELLED) {\n    return 'cancel';\n  } else if (state & STATE_ENDED) {\n    return 'end';\n  } else if (state & STATE_CHANGED) {\n    return 'move';\n  } else if (state & STATE_BEGAN) {\n    return 'start';\n  }\n  return '';\n}\n", "import {\n    STATE_POSSIBLE,\n    STATE_ENDED,\n    STATE_FAILED,\n    STATE_RECOGNIZED,\n    STATE_CANCELLED,\n    STATE_BEGAN,\n    STATE_CHANGED\n} from './recognizer-consts';\nimport assign from '../utils/assign';\nimport uniqueId from '../utils/unique-id';\nimport invokeArrayArg from '../utils/invoke-array-arg';\nimport inArray from '../utils/in-array';\nimport boolOrFn from '../utils/bool-or-fn';\nimport getRecognizerByNameIfManager from './get-recognizer-by-name-if-manager';\nimport stateStr from './state-str';\n\n/**\n * @private\n * Recognizer flow explained; *\n * All recognizers have the initial state of POSSIBLE when a input session starts.\n * The definition of a input session is from the first input until the last input, with all it's movement in it. *\n * Example session for mouse-input: mousedown -> mousemove -> mouseup\n *\n * On each recognizing cycle (see Manager.recognize) the .recognize() method is executed\n * which determines with state it should be.\n *\n * If the recognizer has the state FAILED, <PERSON>NC<PERSON>LED or RECOGNIZED (equals ENDED), it is reset to\n * POSSIBLE to give it another change on the next cycle.\n *\n *               Possible\n *                  |\n *            +-----+---------------+\n *            |                     |\n *      +-----+-----+               |\n *      |           |               |\n *   Failed      Cancelled          |\n *                          +-------+------+\n *                          |              |\n *                      Recognized       Began\n *                                         |\n *                                      Changed\n *                                         |\n *                                  Ended/Recognized\n */\n\n/**\n * @private\n * Recognizer\n * Every recognizer needs to extend from this class.\n * @constructor\n * @param {Object} options\n */\nexport default class Recognizer {\n  constructor(options = {}) {\n    this.options = {\n      enable: true,\n      ...options,\n    };\n\n    this.id = uniqueId();\n\n    this.manager = null;\n\n    // default is enable true\n    this.state = STATE_POSSIBLE;\n    this.simultaneous = {};\n    this.requireFail = [];\n  }\n\n  /**\n   * @private\n   * set options\n   * @param {Object} options\n   * @return {Recognizer}\n   */\n  set(options) {\n    assign(this.options, options);\n\n    // also update the touchAction, in case something changed about the directions/enabled state\n    this.manager && this.manager.touchAction.update();\n    return this;\n  }\n\n  /**\n   * @private\n   * recognize simultaneous with an other recognizer.\n   * @param {Recognizer} otherRecognizer\n   * @returns {Recognizer} this\n   */\n  recognizeWith(otherRecognizer) {\n    if (invokeArrayArg(otherRecognizer, 'recognizeWith', this)) {\n      return this;\n    }\n\n    let { simultaneous } = this;\n    otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n    if (!simultaneous[otherRecognizer.id]) {\n      simultaneous[otherRecognizer.id] = otherRecognizer;\n      otherRecognizer.recognizeWith(this);\n    }\n    return this;\n  }\n\n  /**\n   * @private\n   * drop the simultaneous link. it doesnt remove the link on the other recognizer.\n   * @param {Recognizer} otherRecognizer\n   * @returns {Recognizer} this\n   */\n  dropRecognizeWith(otherRecognizer) {\n    if (invokeArrayArg(otherRecognizer, 'dropRecognizeWith', this)) {\n      return this;\n    }\n\n    otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n    delete this.simultaneous[otherRecognizer.id];\n    return this;\n  }\n\n  /**\n   * @private\n   * recognizer can only run when an other is failing\n   * @param {Recognizer} otherRecognizer\n   * @returns {Recognizer} this\n   */\n  requireFailure(otherRecognizer) {\n    if (invokeArrayArg(otherRecognizer, 'requireFailure', this)) {\n      return this;\n    }\n\n    let { requireFail } = this;\n    otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n    if (inArray(requireFail, otherRecognizer) === -1) {\n      requireFail.push(otherRecognizer);\n      otherRecognizer.requireFailure(this);\n    }\n    return this;\n  }\n\n  /**\n   * @private\n   * drop the requireFailure link. it does not remove the link on the other recognizer.\n   * @param {Recognizer} otherRecognizer\n   * @returns {Recognizer} this\n   */\n  dropRequireFailure(otherRecognizer) {\n    if (invokeArrayArg(otherRecognizer, 'dropRequireFailure', this)) {\n      return this;\n    }\n\n    otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n    let index = inArray(this.requireFail, otherRecognizer);\n    if (index > -1) {\n      this.requireFail.splice(index, 1);\n    }\n    return this;\n  }\n\n  /**\n   * @private\n   * has require failures boolean\n   * @returns {boolean}\n   */\n  hasRequireFailures() {\n    return this.requireFail.length > 0;\n  }\n\n  /**\n   * @private\n   * if the recognizer can recognize simultaneous with an other recognizer\n   * @param {Recognizer} otherRecognizer\n   * @returns {Boolean}\n   */\n  canRecognizeWith(otherRecognizer) {\n    return !!this.simultaneous[otherRecognizer.id];\n  }\n\n  /**\n   * @private\n   * You should use `tryEmit` instead of `emit` directly to check\n   * that all the needed recognizers has failed before emitting.\n   * @param {Object} input\n   */\n  emit(input) {\n    let self = this;\n    let { state } = this;\n\n    function emit(event) {\n      self.manager.emit(event, input);\n    }\n\n    // 'panstart' and 'panmove'\n    if (state < STATE_ENDED) {\n      emit(self.options.event + stateStr(state));\n    }\n\n    emit(self.options.event); // simple 'eventName' events\n\n    if (input.additionalEvent) { // additional event(panleft, panright, pinchin, pinchout...)\n      emit(input.additionalEvent);\n    }\n\n    // panend and pancancel\n    if (state >= STATE_ENDED) {\n      emit(self.options.event + stateStr(state));\n    }\n  }\n\n  /**\n   * @private\n   * Check that all the require failure recognizers has failed,\n   * if true, it emits a gesture event,\n   * otherwise, setup the state to FAILED.\n   * @param {Object} input\n   */\n  tryEmit(input) {\n    if (this.canEmit()) {\n      return this.emit(input);\n    }\n    // it's failing anyway\n    this.state = STATE_FAILED;\n  }\n\n  /**\n   * @private\n   * can we emit?\n   * @returns {boolean}\n   */\n  canEmit() {\n    let i = 0;\n    while (i < this.requireFail.length) {\n      if (!(this.requireFail[i].state & (STATE_FAILED | STATE_POSSIBLE))) {\n        return false;\n      }\n      i++;\n    }\n    return true;\n  }\n\n  /**\n   * @private\n   * update the recognizer\n   * @param {Object} inputData\n   */\n  recognize(inputData) {\n    // make a new copy of the inputData\n    // so we can change the inputData without messing up the other recognizers\n    let inputDataClone = assign({}, inputData);\n\n    // is is enabled and allow recognizing?\n    if (!boolOrFn(this.options.enable, [this, inputDataClone])) {\n      this.reset();\n      this.state = STATE_FAILED;\n      return;\n    }\n\n    // reset when we've reached the end\n    if (this.state & (STATE_RECOGNIZED | STATE_CANCELLED | STATE_FAILED)) {\n      this.state = STATE_POSSIBLE;\n    }\n\n    this.state = this.process(inputDataClone);\n\n    // the recognizer has recognized a gesture\n    // so trigger an event\n    if (this.state & (STATE_BEGAN | STATE_CHANGED | STATE_ENDED | STATE_CANCELLED)) {\n      this.tryEmit(inputDataClone);\n    }\n  }\n\n  /**\n   * @private\n   * return the state of the recognizer\n   * the actual recognizing happens in this method\n   * @virtual\n   * @param {Object} inputData\n   * @returns {constant} STATE\n   */\n\n  /* jshint ignore:start */\n  process(inputData) { }\n  /* jshint ignore:end */\n\n  /**\n   * @private\n   * return the preferred touch-action\n   * @virtual\n   * @returns {Array}\n   */\n  getTouchAction() { }\n\n  /**\n   * @private\n   * called when the gesture isn't allowed to recognize\n   * like when another is being recognized or it is disabled\n   * @virtual\n   */\n  reset() { }\n}", "import Recognizer from '../recognizerjs/recognizer-constructor';\nimport { TOUCH_ACTION_MANIPULATION } from '../touchactionjs/touchaction-Consts';\nimport {INPUT_START,INPUT_END } from '../inputjs/input-consts';\nimport {\n    STATE_RECOGNIZED,\n    STATE_BEGAN,\n    STATE_FAILED\n} from '../recognizerjs/recognizer-consts';\nimport getDistance from '../inputjs/get-distance';\n\n/**\n * @private\n * A tap is recognized when the pointer is doing a small tap/click. Multiple taps are recognized if they occur\n * between the given interval and position. The delay option can be used to recognize multi-taps without firing\n * a single tap.\n *\n * The eventData from the emitted event contains the property `tapCount`, which contains the amount of\n * multi-taps being recognized.\n * @constructor\n * @extends Recognizer\n */\nexport default class TapRecognizer extends Recognizer {\n  constructor(options = {}) {\n    super({\n      event: 'tap',\n      pointers: 1,\n      taps: 1,\n      interval: 300, // max time between the multi-tap taps\n      time: 250, // max time of the pointer to be down (like finger on the screen)\n      threshold: 9, // a minimal movement is ok, but keep it low\n      posThreshold: 10, // a multi-tap can be a bit off the initial position\n      ...options,\n    });\n\n    // previous time and center,\n    // used for tap counting\n    this.pTime = false;\n    this.pCenter = false;\n\n    this._timer = null;\n    this._input = null;\n    this.count = 0;\n  }\n\n  getTouchAction() {\n    return [TOUCH_ACTION_MANIPULATION];\n  }\n\n  process(input) {\n    let { options } = this;\n\n    let validPointers = input.pointers.length === options.pointers;\n    let validMovement = input.distance < options.threshold;\n    let validTouchTime = input.deltaTime < options.time;\n\n    this.reset();\n\n    if ((input.eventType & INPUT_START) && (this.count === 0)) {\n      return this.failTimeout();\n    }\n\n    // we only allow little movement\n    // and we've reached an end event, so a tap is possible\n    if (validMovement && validTouchTime && validPointers) {\n      if (input.eventType !== INPUT_END) {\n        return this.failTimeout();\n      }\n\n      let validInterval = this.pTime ? (input.timeStamp - this.pTime < options.interval) : true;\n      let validMultiTap = !this.pCenter || getDistance(this.pCenter, input.center) < options.posThreshold;\n\n      this.pTime = input.timeStamp;\n      this.pCenter = input.center;\n\n      if (!validMultiTap || !validInterval) {\n        this.count = 1;\n      } else {\n        this.count += 1;\n      }\n\n      this._input = input;\n\n      // if tap count matches we have recognized it,\n      // else it has began recognizing...\n      let tapCount = this.count % options.taps;\n      if (tapCount === 0) {\n        // no failing requirements, immediately trigger the tap event\n        // or wait as long as the multitap interval to trigger\n        if (!this.hasRequireFailures()) {\n          return STATE_RECOGNIZED;\n        } else {\n          this._timer = setTimeout(() => {\n            this.state = STATE_RECOGNIZED;\n            this.tryEmit();\n          }, options.interval);\n          return STATE_BEGAN;\n        }\n      }\n    }\n    return STATE_FAILED;\n  }\n\n  failTimeout() {\n    this._timer = setTimeout(() => {\n      this.state = STATE_FAILED;\n    }, this.options.interval);\n    return STATE_FAILED;\n  }\n\n  reset() {\n    clearTimeout(this._timer);\n  }\n\n  emit() {\n    if (this.state === STATE_RECOGNIZED) {\n      this._input.tapCount = this.count;\n      this.manager.emit(this.options.event, this._input);\n    }\n  }\n}\n", "import Recognizer from '../recognizerjs/recognizer-constructor';\nimport {\n    STATE_BEGAN,\n    STATE_CHANGED,\n    STATE_CANCELLED,\n    STATE_ENDED,\n    STATE_FAILED\n} from '../recognizerjs/recognizer-consts';\nimport {\n    INPUT_CANCEL,\n    INPUT_END\n} from '../inputjs/input-consts';\n\n/**\n * @private\n * This recognizer is just used as a base for the simple attribute recognizers.\n * @constructor\n * @extends Recognizer\n */\nexport default class AttrRecognizer extends Recognizer {\n  constructor(options = {}) {\n    super({\n      pointers: 1,\n      ...options,\n    });\n  }\n\n  /**\n   * @private\n   * Used to check if it the recognizer receives valid input, like input.distance > 10.\n   * @memberof AttrRecognizer\n   * @param {Object} input\n   * @returns {Boolean} recognized\n   */\n  attrTest(input) {\n    let optionPointers = this.options.pointers;\n    return optionPointers === 0 || input.pointers.length === optionPointers;\n  }\n\n  /**\n   * @private\n   * Process the input and return the state for the recognizer\n   * @memberof AttrRecognizer\n   * @param {Object} input\n   * @returns {*} State\n   */\n  process(input) {\n    let { state } = this;\n    let { eventType } = input;\n\n    let isRecognized = state & (STATE_BEGAN | STATE_CHANGED);\n    let isValid = this.attrTest(input);\n\n    // on cancel input and we've recognized before, return STATE_CANCELLED\n    if (isRecognized && (eventType & INPUT_CANCEL || !isValid)) {\n      return state | STATE_CANCELLED;\n    } else if (isRecognized || isValid) {\n      if (eventType & INPUT_END) {\n        return state | STATE_ENDED;\n      } else if (!(state & STATE_BEGAN)) {\n        return STATE_BEGAN;\n      }\n      return state | STATE_CHANGED;\n    }\n    return STATE_FAILED;\n  }\n}\n", "import {\n    DIRECTION_LEFT,\n    DIRECTION_RIGHT,\n    DIRECTION_UP,\n    DIRECTION_DOWN\n} from '../inputjs/input-consts';\n\n/**\n * @private\n * direction cons to string\n * @param {constant} direction\n * @returns {String}\n */\nexport default function directionStr(direction) {\n  if (direction === DIRECTION_DOWN) {\n    return 'down';\n  } else if (direction === DIRECTION_UP) {\n    return 'up';\n  } else if (direction === DIRECTION_LEFT) {\n    return 'left';\n  } else if (direction === DIRECTION_RIGHT) {\n    return 'right';\n  }\n  return '';\n}\n", "import  AttrR<PERSON>ognizer from './attribute';\nimport {\n    DIRECTION_ALL,\n    DIRECTION_HORIZONTAL,\n    DIRECTION_VERTICAL,\n    DIRECTION_NONE,\n    DIRECTION_UP,\n    DIRECTION_DOWN,\n    DIRECTION_LEFT,\n    DIRECTION_RIGHT\n} from '../inputjs/input-consts';\nimport { STATE_BEGAN } from '../recognizerjs/recognizer-consts';\nimport { TOUCH_ACTION_PAN_X,TOUCH_ACTION_PAN_Y } from '../touchactionjs/touchaction-Consts';\nimport directionStr from '../recognizerjs/direction-str';\n\n/**\n * @private\n * Pan\n * Recognized when the pointer is down and moved in the allowed direction.\n * @constructor\n * @extends AttrRecognizer\n */\nexport default class PanRecognizer extends AttrRecognizer {\n  constructor(options = {}) {\n    super({\n      event: 'pan',\n      threshold: 10,\n      pointers: 1,\n      direction: DIRECTION_ALL,\n      ...options,\n    });\n    this.pX = null;\n    this.pY = null;\n  }\n\n  getTouchAction() {\n    let { options:{ direction } } = this;\n    let actions = [];\n    if (direction & DIRECTION_HORIZONTAL) {\n      actions.push(TOUCH_ACTION_PAN_Y);\n    }\n    if (direction & DIRECTION_VERTICAL) {\n      actions.push(TOUCH_ACTION_PAN_X);\n    }\n    return actions;\n  }\n\n  directionTest(input) {\n    let { options } = this;\n    let hasMoved = true;\n    let { distance } = input;\n    let { direction } = input;\n    let x = input.deltaX;\n    let y = input.deltaY;\n\n    // lock to axis?\n    if (!(direction & options.direction)) {\n      if (options.direction & DIRECTION_HORIZONTAL) {\n        direction = (x === 0) ? DIRECTION_NONE : (x < 0) ? DIRECTION_LEFT : DIRECTION_RIGHT;\n        hasMoved = x !== this.pX;\n        distance = Math.abs(input.deltaX);\n      } else {\n        direction = (y === 0) ? DIRECTION_NONE : (y < 0) ? DIRECTION_UP : DIRECTION_DOWN;\n        hasMoved = y !== this.pY;\n        distance = Math.abs(input.deltaY);\n      }\n    }\n    input.direction = direction;\n    return hasMoved && distance > options.threshold && direction & options.direction;\n  }\n\n  attrTest(input) {\n    return AttrRecognizer.prototype.attrTest.call(this, input) && // replace with a super call\n        (this.state & STATE_BEGAN || (!(this.state & STATE_BEGAN) && this.directionTest(input)));\n  }\n\n  emit(input) {\n\n    this.pX = input.deltaX;\n    this.pY = input.deltaY;\n\n    let direction = directionStr(input.direction);\n\n    if (direction) {\n      input.additionalEvent = this.options.event + direction;\n    }\n    super.emit(input);\n  }\n}\n", "import AttrRecognizer from '../recognizers/attribute';\nimport { abs } from '../utils/utils-consts';\nimport { DIRECTION_HORIZONTAL,DIRECTION_VERTICAL } from '../inputjs/input-consts';\nimport PanRecognizer from './pan';\nimport { INPUT_END } from '../inputjs/input-consts';\nimport directionStr from '../recognizerjs/direction-str';\n\n/**\n * @private\n * Swipe\n * Recognized when the pointer is moving fast (velocity), with enough distance in the allowed direction.\n * @constructor\n * @extends AttrRecognizer\n */\nexport default class SwipeRecognizer extends AttrRecognizer {\n  constructor(options = {}) {\n    super({\n      event: 'swipe',\n      threshold: 10,\n      velocity: 0.3,\n      direction: DIRECTION_HORIZONTAL | DIRECTION_VERTICAL,\n      pointers: 1,\n      ...options,\n    });\n  }\n\n  getTouchAction() {\n    return PanRecognizer.prototype.getTouchAction.call(this);\n  }\n\n  attrTest(input) {\n    let { direction } = this.options;\n    let velocity;\n\n    if (direction & (DIRECTION_HORIZONTAL | DIRECTION_VERTICAL)) {\n      velocity = input.overallVelocity;\n    } else if (direction & DIRECTION_HORIZONTAL) {\n      velocity = input.overallVelocityX;\n    } else if (direction & DIRECTION_VERTICAL) {\n      velocity = input.overallVelocityY;\n    }\n\n    return super.attrTest(input) &&\n        direction & input.offsetDirection &&\n        input.distance > this.options.threshold &&\n        input.maxPointers === this.options.pointers &&\n        abs(velocity) > this.options.velocity && input.eventType & INPUT_END;\n  }\n\n  emit(input) {\n    let direction = directionStr(input.offsetDirection);\n    if (direction) {\n      this.manager.emit(this.options.event + direction, input);\n    }\n\n    this.manager.emit(this.options.event, input);\n  }\n}\n", "import AttrRecognizer from './attribute';\nimport { TOUCH_ACTION_NONE } from '../touchactionjs/touchaction-Consts';\nimport { STATE_BEGAN } from '../recognizerjs/recognizer-consts';\n\n/**\n * @private\n * Pinch\n * Recognized when two or more pointers are moving toward (zoom-in) or away from each other (zoom-out).\n * @constructor\n * @extends AttrRecognizer\n */\nexport default class PinchRecognizer extends AttrRecognizer {\n  constructor(options = {}) {\n    super({\n      event: 'pinch',\n      threshold: 0,\n      pointers: 2,\n      ...options,\n    });\n  }\n\n  getTouchAction() {\n    return [TOUCH_ACTION_NONE];\n  }\n\n  attrTest(input) {\n    return super.attrTest(input) &&\n        (Math.abs(input.scale - 1) > this.options.threshold || this.state & STATE_BEGAN);\n  }\n\n  emit(input) {\n    if (input.scale !== 1) {\n      let inOut = input.scale < 1 ? 'in' : 'out';\n      input.additionalEvent = this.options.event + inOut;\n    }\n    super.emit(input);\n  }\n}\n", "import AttrRecognizer from './attribute';\nimport { TOUCH_ACTION_NONE } from '../touchactionjs/touchaction-Consts';\nimport { STATE_BEGAN } from '../recognizerjs/recognizer-consts';\n\n/**\n * @private\n * Rotate\n * Recognized when two or more pointer are moving in a circular motion.\n * @constructor\n * @extends AttrRecognizer\n */\nexport default class RotateRecognizer extends AttrRecognizer {\n  constructor(options = {}) {\n    super( {\n      event: 'rotate',\n      threshold: 0,\n      pointers: 2,\n      ...options,\n    });\n  }\n\n  getTouchAction() {\n    return [TOUCH_ACTION_NONE];\n  }\n\n  attrTest(input) {\n    return super.attrTest(input) &&\n        (Math.abs(input.rotation) > this.options.threshold || this.state & STATE_BEGAN);\n  }\n}", "import Recognizer from '../recognizerjs/recognizer-constructor';\nimport {\n    STATE_RECOGNIZED,\n    STATE_FAILED\n} from '../recognizerjs/recognizer-consts';\nimport { now } from '../utils/utils-consts';\nimport { TOUCH_ACTION_AUTO } from '../touchactionjs/touchaction-Consts';\nimport {\n    INPUT_START,\n    INPUT_END,\n    INPUT_CANCEL\n} from '../inputjs/input-consts';\n\n/**\n * @private\n * Press\n * Recognized when the pointer is down for x ms without any movement.\n * @constructor\n * @extends Recognizer\n */\nexport default class PressRecognizer extends Recognizer {\n  constructor(options = {}) {\n    super({\n      event: 'press',\n      pointers: 1,\n      time: 251, // minimal time of the pointer to be pressed\n      threshold: 9, // a minimal movement is ok, but keep it low\n      ...options,\n    });\n    this._timer = null;\n    this._input = null;\n  }\n\n  getTouchAction() {\n    return [TOUCH_ACTION_AUTO];\n  }\n\n  process(input) {\n    let { options } = this;\n    let validPointers = input.pointers.length === options.pointers;\n    let validMovement = input.distance < options.threshold;\n    let validTime = input.deltaTime > options.time;\n\n    this._input = input;\n\n    // we only allow little movement\n    // and we've reached an end event, so a tap is possible\n    if (!validMovement || !validPointers || (input.eventType & (INPUT_END | INPUT_CANCEL) && !validTime)) {\n      this.reset();\n    } else if (input.eventType & INPUT_START) {\n      this.reset();\n      this._timer = setTimeout(() => {\n        this.state = STATE_RECOGNIZED;\n        this.tryEmit();\n      }, options.time);\n    } else if (input.eventType & INPUT_END) {\n      return STATE_RECOGNIZED;\n    }\n    return STATE_FAILED;\n  }\n\n  reset() {\n    clearTimeout(this._timer);\n  }\n\n  emit(input) {\n    if (this.state !== STATE_RECOGNIZED) {\n      return;\n    }\n\n    if (input && (input.eventType & INPUT_END)) {\n      this.manager.emit(`${this.options.event}up`, input);\n    } else {\n      this._input.timeStamp = now();\n      this.manager.emit(this.options.event, this._input);\n    }\n  }\n}\n\n", "import { TOUCH_ACTION_COMPUTE } from \"./touchactionjs/touchaction-Consts\";\nimport <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from \"./recognizers/tap\";\nimport PanRecognizer from \"./recognizers/pan\";\nimport <PERSON>wipe<PERSON><PERSON>ognizer from \"./recognizers/swipe\";\nimport <PERSON>nch<PERSON><PERSON>ognizer from \"./recognizers/pinch\";\nimport <PERSON><PERSON><PERSON><PERSON><PERSON>ognizer from \"./recognizers/rotate\";\nimport <PERSON><PERSON><PERSON>ognizer from \"./recognizers/press\";\nimport {DIRECTION_HORIZONTAL} from \"./inputjs/input-consts\";\n\nexport default {\n\t/**\n\t * @private\n\t * set if DOM events are being triggered.\n\t * But this is slower and unused by simple implementations, so disabled by default.\n\t * @type {Boolean}\n\t * @default false\n\t */\n\tdomEvents: false,\n\n\t/**\n\t * @private\n\t * The value for the touchAction property/fallback.\n\t * When set to `compute` it will magically set the correct value based on the added recognizers.\n\t * @type {String}\n\t * @default compute\n\t */\n\ttouchAction: TOUCH_ACTION_COMPUTE,\n\n\t/**\n\t * @private\n\t * @type {Boolean}\n\t * @default true\n\t */\n\tenable: true,\n\n\t/**\n\t * @private\n\t * EXPERIMENTAL FEATURE -- can be removed/changed\n\t * Change the parent input target element.\n\t * If Null, then it is being set the to main element.\n\t * @type {Null|EventTarget}\n\t * @default null\n\t */\n\tinputTarget: null,\n\n\t/**\n\t * @private\n\t * force an input class\n\t * @type {Null|Function}\n\t * @default null\n\t */\n\tinputClass: null,\n\n\t/**\n\t * @private\n\t * Some CSS properties can be used to improve the working of Hammer.\n\t * Add them to this method and they will be set when creating a new Manager.\n\t * @namespace\n\t */\n\tcssProps: {\n\t\t/**\n\t\t * @private\n\t\t * Disables text selection to improve the dragging gesture. Mainly for desktop browsers.\n\t\t * @type {String}\n\t\t * @default 'none'\n\t\t */\n\t\tuserSelect: \"none\",\n\n\t\t/**\n\t\t * @private\n\t\t * Disable the Windows Phone grippers when pressing an element.\n\t\t * @type {String}\n\t\t * @default 'none'\n\t\t */\n\t\ttouchSelect: \"none\",\n\n\t\t/**\n\t\t * @private\n\t\t * Disables the default callout shown when you touch and hold a touch target.\n\t\t * On iOS, when you touch and hold a touch target such as a link, Safari displays\n\t\t * a callout containing information about the link. This property allows you to disable that callout.\n\t\t * @type {String}\n\t\t * @default 'none'\n\t\t */\n\t\ttouchCallout: \"none\",\n\n\t\t/**\n\t\t * @private\n\t\t * Specifies whether zooming is enabled. Used by IE10>\n\t\t * @type {String}\n\t\t * @default 'none'\n\t\t */\n\t\tcontentZooming: \"none\",\n\n\t\t/**\n\t\t * @private\n\t\t * Specifies that an entire element should be draggable instead of its contents. Mainly for desktop browsers.\n\t\t * @type {String}\n\t\t * @default 'none'\n\t\t */\n\t\tuserDrag: \"none\",\n\n\t\t/**\n\t\t * @private\n\t\t * Overrides the highlight color shown when the user taps a link or a JavaScript\n\t\t * clickable element in iOS. This property obeys the alpha value, if specified.\n\t\t * @type {String}\n\t\t * @default 'rgba(0,0,0,0)'\n\t\t */\n\t\ttapHighlightColor: \"rgba(0,0,0,0)\",\n\t},\n};\n\n/**\n * @private\n * Default recognizer setup when calling `Hammer()`\n * When creating a new Manager these will be skipped.\n * This is separated with other defaults because of tree-shaking.\n * @type {Array}\n */\nexport const preset = [\n  [RotateRecognizer, { enable: false }],\n  [PinchRecognizer, { enable: false }, ['rotate']],\n  [SwipeRecognizer, { direction: DIRECTION_HORIZONTAL }],\n  [PanRecognizer, { direction: DIRECTION_HORIZONTAL }, ['swipe']],\n  [TapRecognizer],\n  [TapRecognizer, { event: 'doubletap', taps: 2 }, ['tap']],\n  [PressRecognizer]\n];\n", "import assign from \"./utils/assign\";\nimport TouchAction from \"./touchactionjs/touchaction-constructor\";\nimport createInputInstance from \"./inputjs/create-input-instance\";\nimport each from \"./utils/each\";\nimport inArray from \"./utils/in-array\";\nimport invokeArrayArg from \"./utils/invoke-array-arg\";\nimport splitStr from \"./utils/split-str\";\nimport prefixed from \"./utils/prefixed\";\nimport Recognizer from \"./recognizerjs/recognizer-constructor\";\nimport {\n  STATE_BEGAN,\n  STATE_ENDED,\n  STATE_CHANGED,\n  STATE_RECOGNIZED,\n} from \"./recognizerjs/recognizer-consts\";\nimport defaults from \"./defaults\";\n\nconst STOP = 1;\nconst FORCED_STOP = 2;\n\n\n/**\n * @private\n * add/remove the css properties as defined in manager.options.cssProps\n * @param {Manager} manager\n * @param {Boolean} add\n */\nfunction toggleCssProps(manager, add) {\n  const { element } = manager;\n\n  if (!element.style) {\n    return;\n  }\n  let prop;\n\n  each(manager.options.cssProps, (value, name) => {\n    prop = prefixed(element.style, name);\n    if (add) {\n      manager.oldCssProps[prop] = element.style[prop];\n      element.style[prop] = value;\n    } else {\n      element.style[prop] = manager.oldCssProps[prop] || \"\";\n    }\n  });\n  if (!add) {\n    manager.oldCssProps = {};\n  }\n}\n\n/**\n * @private\n * trigger dom event\n * @param {String} event\n * @param {Object} data\n */\nfunction triggerDomEvent(event, data) {\n  const gestureEvent = document.createEvent(\"Event\");\n\n  gestureEvent.initEvent(event, true, true);\n  gestureEvent.gesture = data;\n  data.target.dispatchEvent(gestureEvent);\n}\n\n\n/**\n* @private\n * Manager\n * @param {HTMLElement} element\n * @param {Object} [options]\n * @constructor\n */\nexport default class Manager {\n  constructor(element, options) {\n    this.options = assign({}, defaults, options || {});\n\n    this.options.inputTarget = this.options.inputTarget || element;\n\n    this.handlers = {};\n    this.session = {};\n    this.recognizers = [];\n    this.oldCssProps = {};\n\n    this.element = element;\n    this.input = createInputInstance(this);\n    this.touchAction = new TouchAction(this, this.options.touchAction);\n\n    toggleCssProps(this, true);\n\n    each(this.options.recognizers, item => {\n      const recognizer = this.add(new (item[0])(item[1]));\n\n      item[2] && recognizer.recognizeWith(item[2]);\n      item[3] && recognizer.requireFailure(item[3]);\n    }, this);\n  }\n\n\t/**\n\t * @private\n\t * set options\n\t * @param {Object} options\n\t * @returns {Manager}\n\t */\n  set(options) {\n    assign(this.options, options);\n\n    // Options that need a little more setup\n    if (options.touchAction) {\n      this.touchAction.update();\n    }\n    if (options.inputTarget) {\n      // Clean up existing event listeners and reinitialize\n      this.input.destroy();\n      this.input.target = options.inputTarget;\n      this.input.init();\n    }\n    return this;\n  }\n\n\t/**\n\t * @private\n\t * stop recognizing for this session.\n\t * This session will be discarded, when a new [input]start event is fired.\n\t * When forced, the recognizer cycle is stopped immediately.\n\t * @param {Boolean} [force]\n\t */\n  stop(force) {\n    this.session.stopped = force ? FORCED_STOP : STOP;\n  }\n\n\t/**\n\t * @private\n\t * run the recognizers!\n\t * called by the inputHandler function on every movement of the pointers (touches)\n\t * it walks through all the recognizers and tries to detect the gesture that is being made\n\t * @param {Object} inputData\n\t */\n  recognize(inputData) {\n    const { session } = this;\n\n    if (session.stopped) {\n      return;\n    }\n\n    // run the touch-action polyfill\n    this.touchAction.preventDefaults(inputData);\n\n    let recognizer;\n    const { recognizers } = this;\n\n    // this holds the recognizer that is being recognized.\n    // so the recognizer's state needs to be BEGAN, CHANGED, ENDED or RECOGNIZED\n    // if no recognizer is detecting a thing, it is set to `null`\n    let { curRecognizer } = session;\n\n    // reset when the last recognizer is recognized\n    // or when we're in a new session\n    if (!curRecognizer || (curRecognizer && curRecognizer.state & STATE_RECOGNIZED)) {\n      session.curRecognizer = null;\n      curRecognizer = null;\n    }\n\n    let i = 0;\n\n    while (i < recognizers.length) {\n      recognizer = recognizers[i];\n\n      // find out if we are allowed try to recognize the input for this one.\n      // 1.   allow if the session is NOT forced stopped (see the .stop() method)\n      // 2.   allow if we still haven't recognized a gesture in this session, or the this recognizer is the one\n      //      that is being recognized.\n      // 3.   allow if the recognizer is allowed to run simultaneous with the current recognized recognizer.\n      //      this can be setup with the `recognizeWith()` method on the recognizer.\n      if (session.stopped !== FORCED_STOP && (// 1\n        !curRecognizer || recognizer === curRecognizer || // 2\n        recognizer.canRecognizeWith(curRecognizer))) { // 3\n        recognizer.recognize(inputData);\n      } else {\n        recognizer.reset();\n      }\n\n      // if the recognizer has been recognizing the input as a valid gesture, we want to store this one as the\n      // current active recognizer. but only if we don't already have an active recognizer\n      if (!curRecognizer && recognizer.state & (STATE_BEGAN | STATE_CHANGED | STATE_ENDED)) {\n        session.curRecognizer = recognizer;\n        curRecognizer = recognizer;\n      }\n      i++;\n    }\n  }\n\n\t/**\n\t * @private\n\t * get a recognizer by its event name.\n\t * @param {Recognizer|String} recognizer\n\t * @returns {Recognizer|Null}\n\t */\n  get(recognizer) {\n    if (recognizer instanceof Recognizer) {\n      return recognizer;\n    }\n\n    const { recognizers } = this;\n\n    for (let i = 0; i < recognizers.length; i++) {\n      if (recognizers[i].options.event === recognizer) {\n        return recognizers[i];\n      }\n    }\n    return null;\n  }\n\n\t/**\n\t * @private add a recognizer to the manager\n\t * existing recognizers with the same event name will be removed\n\t * @param {Recognizer} recognizer\n\t * @returns {Recognizer|Manager}\n\t */\n  add(recognizer) {\n    if (invokeArrayArg(recognizer, \"add\", this)) {\n      return this;\n    }\n\n    // remove existing\n    const existing = this.get(recognizer.options.event);\n\n    if (existing) {\n      this.remove(existing);\n    }\n\n    this.recognizers.push(recognizer);\n    recognizer.manager = this;\n\n    this.touchAction.update();\n    return recognizer;\n  }\n\n\t/**\n\t * @private\n\t * remove a recognizer by name or instance\n\t * @param {Recognizer|String} recognizer\n\t * @returns {Manager}\n\t */\n  remove(recognizer) {\n    if (invokeArrayArg(recognizer, \"remove\", this)) {\n      return this;\n    }\n\n    const targetRecognizer = this.get(recognizer);\n\n    // let's make sure this recognizer exists\n    if (recognizer) {\n      const { recognizers } = this;\n      const index = inArray(recognizers, targetRecognizer);\n\n      if (index !== -1) {\n        recognizers.splice(index, 1);\n        this.touchAction.update();\n      }\n    }\n\n    return this;\n  }\n\n\t/**\n\t * @private\n\t * bind event\n\t * @param {String} events\n\t * @param {Function} handler\n\t * @returns {EventEmitter} this\n\t */\n  on(events, handler) {\n    if (events === undefined || handler === undefined) {\n      return this;\n    }\n\n    const { handlers } = this;\n\n    each(splitStr(events), event => {\n      handlers[event] = handlers[event] || [];\n      handlers[event].push(handler);\n    });\n    return this;\n  }\n\n\t/**\n\t * @private unbind event, leave emit blank to remove all handlers\n\t * @param {String} events\n\t * @param {Function} [handler]\n\t * @returns {EventEmitter} this\n\t */\n  off(events, handler) {\n    if (events === undefined) {\n      return this;\n    }\n\n    const { handlers } = this;\n\n    each(splitStr(events), event => {\n      if (!handler) {\n        delete handlers[event];\n      } else {\n        handlers[event] && handlers[event].splice(inArray(handlers[event], handler), 1);\n      }\n    });\n    return this;\n  }\n\n\t/**\n\t * @private emit event to the listeners\n\t * @param {String} event\n\t * @param {Object} data\n\t */\n  emit(event, data) {\n    // we also want to trigger dom events\n    if (this.options.domEvents) {\n      triggerDomEvent(event, data);\n    }\n\n    // no handlers, so skip it all\n    const handlers = this.handlers[event] && this.handlers[event].slice();\n\n    if (!handlers || !handlers.length) {\n      return;\n    }\n\n    data.type = event;\n    data.preventDefault = function () {\n      data.srcEvent.preventDefault();\n    };\n\n    let i = 0;\n\n    while (i < handlers.length) {\n      handlers[i](data);\n      i++;\n    }\n  }\n\n\t/**\n\t * @private\n\t * destroy the manager and unbinds all events\n\t * it doesn't unbind dom events, that is the user own responsibility\n\t */\n  destroy() {\n    this.element && toggleCssProps(this, false);\n\n    this.handlers = {};\n    this.session = {};\n    this.input.destroy();\n    this.element = null;\n  }\n}\n", "import {\n    INPUT_START,\n    INPUT_MOVE,\n    INPUT_END,\n    INPUT_CANCEL,\n    INPUT_TYPE_TOUCH\n} from '../inputjs/input-consts';\nimport Input from '../inputjs/input-constructor';\nimport toArray from '../utils/to-array';\nimport uniqueArray from '../utils/unique-array';\n\nconst SINGLE_TOUCH_INPUT_MAP = {\n  touchstart: INPUT_START,\n  touchmove: INPUT_MOVE,\n  touchend: INPUT_END,\n  touchcancel: INPUT_CANCEL\n};\n\nconst SINGLE_TOUCH_TARGET_EVENTS = 'touchstart';\nconst SINGLE_TOUCH_WINDOW_EVENTS = 'touchstart touchmove touchend touchcancel';\n\n/**\n * @private\n * Touch events input\n * @constructor\n * @extends Input\n */\nexport default class SingleTouchInput extends Input {\n  constructor() {\n    var proto = SingleTouchInput.prototype;\n    proto.evTarget = SINGLE_TOUCH_TARGET_EVENTS;\n    proto.evWin = SINGLE_TOUCH_WINDOW_EVENTS;\n\n    super(...arguments);\n    this.started = false;\n  }\n\n  handler(ev) {\n    let type = SINGLE_TOUCH_INPUT_MAP[ev.type];\n\n    // should we handle the touch events?\n    if (type === INPUT_START) {\n      this.started = true;\n    }\n\n    if (!this.started) {\n      return;\n    }\n\n    let touches = normalizeSingleTouches.call(this, ev, type);\n\n    // when done, reset the started state\n    if (type & (INPUT_END | INPUT_CANCEL) && touches[0].length - touches[1].length === 0) {\n      this.started = false;\n    }\n\n    this.callback(this.manager, type, {\n      pointers: touches[0],\n      changedPointers: touches[1],\n      pointerType: INPUT_TYPE_TOUCH,\n      srcEvent: ev\n    });\n  }\n}\n\n/**\n * @private\n * @this {TouchInput}\n * @param {Object} ev\n * @param {Number} type flag\n * @returns {undefined|Array} [all, changed]\n */\nfunction normalizeSingleTouches(ev, type) {\n  let all = toArray(ev.touches);\n  let changed = toArray(ev.changedTouches);\n\n  if (type & (INPUT_END | INPUT_CANCEL)) {\n    all = uniqueArray(all.concat(changed), 'identifier', true);\n  }\n\n  return [all, changed];\n}\n", "/**\n * @private\n * wrap a method with a deprecation warning and stack trace\n * @param {Function} method\n * @param {String} name\n * @param {String} message\n * @returns {Function} A new function wrapping the supplied method.\n */\nexport default function deprecate(method, name, message) {\n  let deprecationMessage = `DEPRECATED METHOD: ${name}\\n${message} AT \\n`;\n  return function() {\n    let e = new Error('get-stack-trace');\n    let stack = e && e.stack ? e.stack.replace(/^[^\\(]+?[\\n$]/gm, '')\n        .replace(/^\\s+at\\s+/gm, '')\n        .replace(/^Object.<anonymous>\\s*\\(/gm, '{anonymous}()@') : 'Unknown Stack Trace';\n\n    let log = window.console && (window.console.warn || window.console.log);\n    if (log) {\n      log.call(window.console, deprecationMessage, stack);\n    }\n    return method.apply(this, arguments);\n  };\n}\n", "import deprecate from './deprecate';\n/**\n * @private\n * extend object.\n * means that properties in dest will be overwritten by the ones in src.\n * @param {Object} dest\n * @param {Object} src\n * @param {Boolean} [merge=false]\n * @returns {Object} dest\n */\nconst extend = deprecate((dest, src, merge) => {\n  let keys = Object.keys(src);\n  let i = 0;\n  while (i < keys.length) {\n    if (!merge || (merge && dest[keys[i]] === undefined)) {\n      dest[keys[i]] = src[keys[i]];\n    }\n    i++;\n  }\n  return dest;\n}, 'extend', 'Use `assign`.');\n\nexport default extend;\n", "import deprecate from './deprecate';\nimport extend from './extend';\n/**\n * @private\n * merge the values from src in the dest.\n * means that properties that exist in dest will not be overwritten by src\n * @param {Object} dest\n * @param {Object} src\n * @returns {Object} dest\n */\nconst merge = deprecate((dest, src) => {\n  return extend(dest, src, true);\n}, 'merge', 'Use `assign`.');\n\nexport default merge;\n", "import assign from './assign';\n/**\n * @private\n * simple class inheritance\n * @param {Function} child\n * @param {Function} base\n * @param {Object} [properties]\n */\nexport default function inherit(child, base, properties) {\n  let baseP = base.prototype;\n  let childP;\n\n  childP = child.prototype = Object.create(baseP);\n  childP.constructor = child;\n  childP._super = baseP;\n\n  if (properties) {\n    assign(childP, properties);\n  }\n}\n", "/**\n * @private\n * simple function bind\n * @param {Function} fn\n * @param {Object} context\n * @returns {Function}\n */\nexport default function bindFn(fn, context) {\n  return function boundFn() {\n    return fn.apply(context, arguments);\n  };\n}\n", "import Manager from \"./manager\";\nimport defaults, { preset } from \"./defaults\";\nimport assign from './utils/assign';\nimport {\n  INPUT_START,\n  INPUT_MOVE,\n  INPUT_END,\n  INPUT_CANCEL,\n  DIRECTION_NONE,\n  DIRECTION_LEFT,\n  DIRECTION_RIGHT,\n  DIRECTION_UP,\n  DIRECTION_DOWN,\n  DIRECTION_HORIZONTAL,\n  DIRECTION_VERTICAL,\n  DIRECTION_ALL,\n} from \"./inputjs/input-consts\";\nimport {\n  STATE_POSSIBLE,\n  STATE_BEGAN,\n  STATE_CHANGED,\n  STATE_ENDED,\n  STATE_RECOGNIZED,\n  STATE_CANCELLED,\n  STATE_FAILED,\n} from \"./recognizerjs/recognizer-consts\";\n\nimport Input from \"./inputjs/input-constructor\";\nimport TouchAction from \"./touchactionjs/touchaction-constructor\";\nimport TouchInput from \"./input/touch\";\nimport MouseInput from \"./input/mouse\";\nimport PointerEventInput from \"./input/pointerevent\";\nimport SingleTouchInput from \"./input/singletouch\";\nimport TouchMouseInput from \"./input/touchmouse\";\n\nimport Recognizer from \"./recognizerjs/recognizer-constructor\";\nimport AttrRecognizer from \"./recognizers/attribute\";\nimport TapRecognizer from \"./recognizers/tap\";\nimport PanRecognizer from \"./recognizers/pan\";\nimport SwipeRecognizer from \"./recognizers/swipe\";\nimport PinchRecognizer from \"./recognizers/pinch\";\nimport RotateRecognizer from \"./recognizers/rotate\";\nimport PressRecognizer from \"./recognizers/press\";\n\nimport addEventListeners from \"./utils/add-event-listeners\";\nimport removeEventListeners from \"./utils/remove-event-listeners\";\nimport each from \"./utils/each\";\nimport merge from \"./utils/merge\";\nimport extend from \"./utils/extend\";\nimport inherit from \"./utils/inherit\";\nimport bindFn from \"./utils/bind-fn\";\nimport prefixed from \"./utils/prefixed\";\nimport toArray from \"./utils/to-array\";\nimport uniqueArray from \"./utils/unique-array\";\nimport splitStr from \"./utils/split-str\";\nimport inArray from \"./utils/in-array\";\nimport boolOrFn from \"./utils/bool-or-fn\";\nimport hasParent from \"./utils/has-parent\";\n/**\n * @private\n * Simple way to create a manager with a default set of recognizers.\n * @param {HTMLElement} element\n * @param {Object} [options]\n * @constructor\n */\nexport default class Hammer {\n\t/**\n   * @private\n   * @const {string}\n   */\n\tstatic VERSION = \"#__VERSION__#\";\n\tstatic DIRECTION_ALL = DIRECTION_ALL;\n\tstatic DIRECTION_DOWN = DIRECTION_DOWN;\n\tstatic DIRECTION_LEFT = DIRECTION_LEFT;\n\tstatic DIRECTION_RIGHT = DIRECTION_RIGHT;\n\tstatic DIRECTION_UP = DIRECTION_UP;\n\tstatic DIRECTION_HORIZONTAL = DIRECTION_HORIZONTAL;\n\tstatic DIRECTION_VERTICAL = DIRECTION_VERTICAL;\n\tstatic DIRECTION_NONE = DIRECTION_NONE;\n\tstatic DIRECTION_DOWN = DIRECTION_DOWN;\n\tstatic INPUT_START = INPUT_START;\n\tstatic INPUT_MOVE = INPUT_MOVE;\n  static INPUT_END = INPUT_END;\n\tstatic INPUT_CANCEL = INPUT_CANCEL;\n\tstatic STATE_POSSIBLE = STATE_POSSIBLE;\n\tstatic STATE_BEGAN = STATE_BEGAN;\n\tstatic STATE_CHANGED = STATE_CHANGED;\n\tstatic STATE_ENDED = STATE_ENDED;\n\tstatic STATE_RECOGNIZED = STATE_RECOGNIZED;\n\tstatic STATE_CANCELLED = STATE_CANCELLED;\n\tstatic STATE_FAILED = STATE_FAILED;\n\tstatic Manager = Manager;\n\tstatic Input = Input;\n\tstatic TouchAction = TouchAction;\n\tstatic TouchInput = TouchInput;\n\tstatic MouseInput = MouseInput;\n\tstatic PointerEventInput = PointerEventInput;\n\tstatic TouchMouseInput = TouchMouseInput;\n\tstatic SingleTouchInput = SingleTouchInput;\n\tstatic Recognizer = Recognizer;\n\tstatic AttrRecognizer = AttrRecognizer;\n\tstatic Tap = TapRecognizer;\n\tstatic Pan = PanRecognizer;\n\tstatic Swipe = SwipeRecognizer;\n\tstatic Pinch = PinchRecognizer;\n\tstatic Rotate = RotateRecognizer;\n\tstatic Press = PressRecognizer;\n\tstatic on = addEventListeners;\n\tstatic off = removeEventListeners;\n\tstatic each = each;\n\tstatic merge = merge;\n\tstatic extend = extend;\n\tstatic bindFn = bindFn;\n\tstatic assign = assign;\n\tstatic inherit = inherit;\n\tstatic bindFn = bindFn;\n\tstatic prefixed = prefixed;\n\tstatic toArray = toArray;\n\tstatic inArray = inArray;\n\tstatic uniqueArray = uniqueArray;\n\tstatic splitStr = splitStr;\n\tstatic boolOrFn = boolOrFn;\n\tstatic hasParent = hasParent;\n\tstatic addEventListeners = addEventListeners;\n\tstatic removeEventListeners = removeEventListeners;\n\tstatic defaults = assign({}, defaults, { preset });\n\tconstructor(element, options = {}) {\n\t\treturn new Manager(element, {\n\t\t\trecognizers: [\n        // RecognizerClass, options, [recognizeWith, ...], [requireFailure, ...]\n        ...preset\n\t\t\t],\n\t\t\t...options,\n\t\t});\n\t}\n}\n", "\nimport Hammer from \"./hammer\";\nimport assign from \"./utils/assign\";\n\nimport {\n  INPUT_START,\n  INPUT_MOVE,\n  INPUT_END,\n  INPUT_CANCEL,\n  DIRECTION_NONE,\n  DIRECTION_LEFT,\n  DIRECTION_RIGHT,\n  DIRECTION_UP,\n  DIRECTION_DOWN,\n  DIRECTION_HORIZONTAL,\n  DIRECTION_VERTICAL,\n  DIRECTION_ALL,\n} from \"./inputjs/input-consts\";\nimport {\n  STATE_POSSIBLE,\n  STATE_BEGAN,\n  STATE_CHANGED,\n  STATE_ENDED,\n  STATE_RECOGNIZED,\n  STATE_CANCELLED,\n  STATE_FAILED,\n} from \"./recognizerjs/recognizer-consts\";\n\nimport Manager from \"./manager\";\nimport Input from \"./inputjs/input-constructor\";\nimport TouchAction from \"./touchactionjs/touchaction-constructor\";\nimport TouchInput from \"./input/touch\";\nimport MouseInput from \"./input/mouse\";\nimport PointerEventInput from \"./input/pointerevent\";\nimport SingleTouchInput from \"./input/singletouch\";\nimport TouchMouseInput from \"./input/touchmouse\";\n\nimport Recognizer from \"./recognizerjs/recognizer-constructor\";\nimport AttrRecognizer from \"./recognizers/attribute\";\nimport TapRecognizer from \"./recognizers/tap\";\nimport PanRecognizer from \"./recognizers/pan\";\nimport SwipeRecognizer from \"./recognizers/swipe\";\nimport PinchRecognizer from \"./recognizers/pinch\";\nimport RotateRecognizer from \"./recognizers/rotate\";\nimport PressRecognizer from \"./recognizers/press\";\n\nimport addEventListeners from \"./utils/add-event-listeners\";\nimport removeEventListeners from \"./utils/remove-event-listeners\";\nimport each from \"./utils/each\";\nimport merge from \"./utils/merge\";\nimport extend from \"./utils/extend\";\nimport inherit from \"./utils/inherit\";\nimport bindFn from \"./utils/bind-fn\";\nimport prefixed from \"./utils/prefixed\";\nimport toArray from \"./utils/to-array\";\nimport uniqueArray from \"./utils/unique-array\";\nimport splitStr from \"./utils/split-str\";\nimport inArray from \"./utils/in-array\";\nimport boolOrFn from \"./utils/bool-or-fn\";\nimport hasParent from \"./utils/has-parent\";\n\n// this prevents errors when Hammer is loaded in the presence of an AMD\n//  style loader but by script tag, not by the loader.\n\nconst defaults = Hammer.defaults;\n\nexport {\n  Hammer as default,\n  INPUT_START,\n  INPUT_MOVE,\n  INPUT_END,\n  INPUT_CANCEL,\n  STATE_POSSIBLE,\n  STATE_BEGAN,\n  STATE_CHANGED,\n  STATE_ENDED,\n  STATE_RECOGNIZED,\n  STATE_CANCELLED,\n  STATE_FAILED,\n\n  DIRECTION_NONE,\n  DIRECTION_LEFT,\n  DIRECTION_RIGHT,\n  DIRECTION_UP,\n  DIRECTION_DOWN,\n  DIRECTION_HORIZONTAL,\n  DIRECTION_VERTICAL,\n  DIRECTION_ALL,\n  Manager,\n  Input,\n  TouchAction,\n  TouchInput,\n  MouseInput,\n  PointerEventInput,\n  TouchMouseInput,\n  SingleTouchInput,\n  Recognizer,\n  AttrRecognizer,\n  TapRecognizer as Tap,\n  PanRecognizer as Pan,\n  SwipeRecognizer as Swipe,\n  PinchRecognizer as Pinch,\n  RotateRecognizer as Rotate,\n  PressRecognizer as Press,\n  addEventListeners as on,\n  removeEventListeners as off,\n  each,\n  merge,\n  extend,\n  assign,\n  inherit,\n  bindFn,\n  prefixed,\n  toArray,\n  inArray,\n  uniqueArray,\n  splitStr,\n  boolOrFn,\n  hasParent,\n  addEventListeners,\n  removeEventListeners,\n  defaults,\n};\n"], "names": ["assign", "Object", "target", "undefined", "TypeError", "output", "index", "arguments", "length", "source", "<PERSON><PERSON><PERSON>", "hasOwnProperty", "VENDOR_PREFIXES", "TEST_ELEMENT", "document", "style", "createElement", "TYPE_FUNCTION", "round", "Math", "abs", "now", "Date", "prefixed", "obj", "property", "prefix", "prop", "camelProp", "toUpperCase", "slice", "i", "win", "window", "PREFIXED_TOUCH_ACTION", "NATIVE_TOUCH_ACTION", "getTouchActionProps", "touchMap", "cssSupports", "CSS", "supports", "for<PERSON>ach", "val", "TOUCH_ACTION_COMPUTE", "TOUCH_ACTION_AUTO", "TOUCH_ACTION_MANIPULATION", "TOUCH_ACTION_NONE", "TOUCH_ACTION_PAN_X", "TOUCH_ACTION_PAN_Y", "TOUCH_ACTION_MAP", "MOBILE_REGEX", "SUPPORT_TOUCH", "SUPPORT_POINTER_EVENTS", "SUPPORT_ONLY_TOUCH", "test", "navigator", "userAgent", "INPUT_TYPE_TOUCH", "INPUT_TYPE_PEN", "INPUT_TYPE_MOUSE", "INPUT_TYPE_KINECT", "COMPUTE_INTERVAL", "INPUT_START", "INPUT_MOVE", "INPUT_END", "INPUT_CANCEL", "DIRECTION_NONE", "DIRECTION_LEFT", "DIRECTION_RIGHT", "DIRECTION_UP", "DIRECTION_DOWN", "DIRECTION_HORIZONTAL", "DIRECTION_VERTICAL", "DIRECTION_ALL", "PROPS_XY", "PROPS_CLIENT_XY", "each", "iterator", "context", "call", "boolOrFn", "args", "apply", "inStr", "str", "find", "indexOf", "cleanTouchActions", "actions", "hasPanX", "hasPanY", "TouchAction", "manager", "value", "set", "compute", "element", "toLowerCase", "trim", "update", "options", "touchAction", "recognizers", "recognizer", "enable", "concat", "getTouchAction", "join", "preventDefaults", "input", "srcEvent", "direction", "offsetDirection", "session", "prevented", "preventDefault", "hasNone", "isTapPointer", "pointers", "isTapMovement", "distance", "isTapTouchTime", "deltaTime", "preventSrc", "hasParent", "node", "parent", "parentNode", "getCenter", "pointers<PERSON><PERSON><PERSON>", "x", "clientX", "y", "clientY", "simpleCloneInputData", "timeStamp", "center", "deltaX", "deltaY", "getDistance", "p1", "p2", "props", "sqrt", "getAngle", "atan2", "PI", "getDirection", "computeDeltaXY", "offset", "offsetDelta", "prevDel<PERSON>", "prevInput", "eventType", "getVelocity", "getScale", "start", "end", "getRotation", "computeIntervalInputData", "last", "lastInterval", "velocity", "velocityX", "velocityY", "v", "computeInputData", "firstInput", "firstMultiple", "offsetCenter", "angle", "overallVelocity", "overallVelocityX", "overallVelocityY", "scale", "rotation", "maxPointers", "srcEventTarget", "<PERSON><PERSON><PERSON>", "path", "inputHandler", "pointersLen", "changedPointersLen", "changedPointers", "<PERSON><PERSON><PERSON><PERSON>", "isFinal", "emit", "recognize", "splitStr", "split", "addEventListeners", "types", "handler", "type", "addEventListener", "removeEventListeners", "removeEventListener", "getWindowForElement", "doc", "ownerDocument", "defaultView", "parentWindow", "Input", "callback", "self", "inputTarget", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ev", "init", "evEl", "ev<PERSON><PERSON><PERSON>", "evWin", "destroy", "inArray", "src", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "POINTER_INPUT_MAP", "pointerdown", "pointermove", "pointerup", "pointercancel", "pointerout", "IE10_POINTER_TYPE_ENUM", "POINTER_ELEMENT_EVENTS", "POINTER_WINDOW_EVENTS", "MSPointerEvent", "PointerEvent", "PointerEventInput", "proto", "prototype", "store", "pointerEvents", "removePointer", "eventTypeNormalized", "replace", "pointerType", "is<PERSON><PERSON>ch", "storeIndex", "pointerId", "button", "push", "splice", "toArray", "Array", "uniqueArray", "key", "sort", "results", "values", "a", "b", "TOUCH_INPUT_MAP", "touchstart", "touchmove", "touchend", "touchcancel", "TOUCH_TARGET_EVENTS", "TouchInput", "targetIds", "touches", "getTouches", "allTouches", "identifier", "targetTouches", "changedTouches", "changedTargetTouches", "filter", "touch", "MOUSE_INPUT_MAP", "mousedown", "mousemove", "mouseup", "MOUSE_ELEMENT_EVENTS", "MOUSE_WINDOW_EVENTS", "MouseInput", "pressed", "which", "DEDUP_TIMEOUT", "DEDUP_DISTANCE", "setLastTouch", "eventData", "primaryTouch", "lastTouch", "lts", "lastTouches", "removeLastTouch", "setTimeout", "recordTouches", "isSyntheticEvent", "t", "dx", "dy", "TouchMouseInput", "inputEvent", "inputData", "isMouse", "sourceCapabilities", "firesTouchEvents", "mouse", "createInputInstance", "Type", "inputClass", "invokeArrayArg", "arg", "fn", "isArray", "STATE_POSSIBLE", "STATE_BEGAN", "STATE_CHANGED", "STATE_ENDED", "STATE_RECOGNIZED", "STATE_CANCELLED", "STATE_FAILED", "_uniqueId", "uniqueId", "getRecognizerByNameIfManager", "otherRecognizer", "get", "stateStr", "state", "Recognizer", "id", "simultaneous", "requireFail", "recognizeWith", "dropRecognizeWith", "requireFailure", "dropRequireFailure", "hasRequireFailures", "canRecognizeWith", "event", "additionalEvent", "tryEmit", "canEmit", "inputDataClone", "reset", "process", "TapRecognizer", "taps", "interval", "time", "threshold", "pos<PERSON><PERSON><PERSON><PERSON>", "pTime", "pCenter", "_timer", "_input", "count", "validPointers", "validMovement", "validTouchTime", "failTimeout", "validInterval", "validMultiTap", "tapCount", "clearTimeout", "AttrRecognizer", "attrTest", "optionPointers", "isRecognized", "<PERSON><PERSON><PERSON><PERSON>", "directionStr", "PanRecognizer", "pX", "pY", "directionTest", "hasMoved", "SwipeRecognizer", "PinchRecognizer", "inOut", "RotateRecognizer", "PressRecognizer", "validTime", "domEvents", "cssProps", "userSelect", "touchSelect", "touchCallout", "contentZooming", "userDrag", "tapHighlightColor", "preset", "STOP", "FORCED_STOP", "toggleCssProps", "add", "name", "oldCssProps", "triggerDomEvent", "data", "gestureEvent", "createEvent", "initEvent", "gesture", "dispatchEvent", "Manager", "defaults", "handlers", "item", "stop", "force", "stopped", "cur<PERSON><PERSON><PERSON><PERSON><PERSON>", "existing", "remove", "targetRecognizer", "on", "events", "off", "SINGLE_TOUCH_INPUT_MAP", "SINGLE_TOUCH_TARGET_EVENTS", "SINGLE_TOUCH_WINDOW_EVENTS", "SingleTouchInput", "started", "normalizeSingleTouches", "all", "changed", "deprecate", "method", "message", "deprecationMessage", "e", "Error", "stack", "log", "console", "warn", "extend", "dest", "merge", "keys", "inherit", "child", "base", "properties", "baseP", "childP", "create", "constructor", "_super", "bindFn", "boundFn", "Hammer", "VERSION", "Tap", "Pan", "Swipe", "Pinch", "Rotate", "Press"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;AAQA,IAAIA,MAAJ;;AACA,IAAI,OAAOC,MAAM,CAACD,MAAd,KAAyB,UAA7B,EAAyC;EACvCA,MAAM,GAAG,SAASA,MAAT,CAAgBE,MAAhB,EAAwB;QAC3BA,MAAM,KAAKC,SAAX,IAAwBD,MAAM,KAAK,IAAvC,EAA6C;YACrC,IAAIE,SAAJ,CAAc,4CAAd,CAAN;;;QAGEC,MAAM,GAAGJ,MAAM,CAACC,MAAD,CAAnB;;SACK,IAAII,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGC,SAAS,CAACC,MAAtC,EAA8CF,KAAK,EAAnD,EAAuD;UAC/CG,MAAM,GAAGF,SAAS,CAACD,KAAD,CAAxB;;UACIG,MAAM,KAAKN,SAAX,IAAwBM,MAAM,KAAK,IAAvC,EAA6C;aACtC,IAAMC,OAAX,IAAsBD,MAAtB,EAA8B;cACxBA,MAAM,CAACE,cAAP,CAAsBD,OAAtB,CAAJ,EAAoC;YAClCL,MAAM,CAACK,OAAD,CAAN,GAAkBD,MAAM,CAACC,OAAD,CAAxB;;;;;;WAKDL,MAAP;GAhBF;CADF,MAmBO;EACLL,MAAM,GAAGC,MAAM,CAACD,MAAhB;;;AAGF,eAAeA,MAAf;;AC/BA,IAAMY,eAAe,GAAG,CAAC,EAAD,EAAK,QAAL,EAAe,KAAf,EAAsB,IAAtB,EAA4B,IAA5B,EAAkC,GAAlC,CAAxB;AACA,IAAMC,YAAY,GAAG,OAAOC,QAAP,KAAoB,WAApB,GAAkC;EAACC,KAAK,EAAE;CAA1C,GAAgDD,QAAQ,CAACE,aAAT,CAAuB,KAAvB,CAArE;AAEA,IAAMC,aAAa,GAAG,UAAtB;IAEQC,QAAeC,KAAfD;IAAOE,MAAQD,KAARC;IACPC,MAAQC,KAARD;;ACNR;;;;;;;;AAOA,AAAe,SAASE,QAAT,CAAkBC,GAAlB,EAAuBC,QAAvB,EAAiC;MAC1CC,MAAJ;MACIC,IAAJ;MACIC,SAAS,GAAGH,QAAQ,CAAC,CAAD,CAAR,CAAYI,WAAZ,KAA4BJ,QAAQ,CAACK,KAAT,CAAe,CAAf,CAA5C;MAEIC,CAAC,GAAG,CAAR;;SACOA,CAAC,GAAGnB,eAAe,CAACJ,MAA3B,EAAmC;IACjCkB,MAAM,GAAGd,eAAe,CAACmB,CAAD,CAAxB;IACAJ,IAAI,GAAID,MAAD,GAAWA,MAAM,GAAGE,SAApB,GAAgCH,QAAvC;;QAEIE,IAAI,IAAIH,GAAZ,EAAiB;aACRG,IAAP;;;IAEFI,CAAC;;;SAEI5B,SAAP;;;ACvBF;AAEA,IAAI6B,GAAJ;;AAEA,IAAI,OAAOC,MAAP,KAAkB,WAAtB,EAAmC;;EAElCD,GAAG,GAAG,EAAN;CAFD,MAGO;EACNA,GAAG,GAAGC,MAAN;;;ACJM,IAAMC,qBAAqB,GAAGX,QAAQ,CAACV,YAAY,CAACE,KAAd,EAAqB,aAArB,CAAtC;AACP,AAAO,IAAMoB,mBAAmB,GAAGD,qBAAqB,KAAK/B,SAAtD;AAEP,AAAe,SAASiC,mBAAT,GAA+B;MACxC,CAACD,mBAAL,EAA0B;WACjB,KAAP;;;MAEEE,QAAQ,GAAG,EAAf;MACIC,WAAW,GAAGL,GAAM,CAACM,GAAP,IAAcN,GAAM,CAACM,GAAP,CAAWC,QAA3C;GACC,MAAD,EAAS,cAAT,EAAyB,OAAzB,EAAkC,OAAlC,EAA2C,aAA3C,EAA0D,MAA1D,EAAkEC,OAAlE,CAA0E,UAACC,GAAD,EAAS;;;WAI1EL,QAAQ,CAACK,GAAD,CAAR,GAAgBJ,WAAW,GAAGL,GAAM,CAACM,GAAP,CAAWC,QAAX,CAAoB,cAApB,EAAoCE,GAApC,CAAH,GAA8C,IAAhF;GAJF;SAMOL,QAAP;;;ACdF,IAAMM,oBAAoB,GAAG,SAA7B;AACA,IAAMC,iBAAiB,GAAG,MAA1B;AACA,IAAMC,yBAAyB,GAAG,cAAlC;;AACA,IAAMC,iBAAiB,GAAG,MAA1B;AACA,IAAMC,kBAAkB,GAAG,OAA3B;AACA,IAAMC,kBAAkB,GAAG,OAA3B;AACA,IAAMC,gBAAgB,GAAGb,mBAAmB,EAA5C;;ACRA,IAAMc,YAAY,GAAG,uCAArB;AAEA,IAAMC,aAAa,GAAI,kBAAkBlB,GAAzC;AACA,IAAMmB,sBAAsB,GAAG7B,QAAQ,CAACU,GAAD,EAAS,cAAT,CAAR,KAAqC9B,SAApE;AACA,IAAMkD,kBAAkB,GAAGF,aAAa,IAAID,YAAY,CAACI,IAAb,CAAkBC,SAAS,CAACC,SAA5B,CAA5C;AAEA,IAAMC,gBAAgB,GAAG,OAAzB;AACA,IAAMC,cAAc,GAAG,KAAvB;AACA,IAAMC,gBAAgB,GAAG,OAAzB;AACA,IAAMC,iBAAiB,GAAG,QAA1B;AAEA,IAAMC,gBAAgB,GAAG,EAAzB;AAEA,IAAMC,WAAW,GAAG,CAApB;AACA,IAAMC,UAAU,GAAG,CAAnB;AACA,IAAMC,SAAS,GAAG,CAAlB;AACA,IAAMC,YAAY,GAAG,CAArB;AAEA,IAAMC,cAAc,GAAG,CAAvB;AACA,IAAMC,cAAc,GAAG,CAAvB;AACA,IAAMC,eAAe,GAAG,CAAxB;AACA,IAAMC,YAAY,GAAG,CAArB;AACA,IAAMC,cAAc,GAAG,EAAvB;AAEA,IAAMC,oBAAoB,GAAGJ,cAAc,GAAGC,eAA9C;AACA,IAAMI,kBAAkB,GAAGH,YAAY,GAAGC,cAA1C;AACA,IAAMG,aAAa,GAAGF,oBAAoB,GAAGC,kBAA7C;AAEA,IAAME,QAAQ,GAAG,CAAC,GAAD,EAAM,GAAN,CAAjB;AACA,IAAMC,eAAe,GAAG,CAAC,SAAD,EAAY,SAAZ,CAAxB;;AChCA;;;;;;;AAOA,AAAe,SAASC,IAAT,CAAcpD,GAAd,EAAmBqD,QAAnB,EAA6BC,OAA7B,EAAsC;MAC/C/C,CAAJ;;MAEI,CAACP,GAAL,EAAU;;;;MAINA,GAAG,CAACiB,OAAR,EAAiB;IACfjB,GAAG,CAACiB,OAAJ,CAAYoC,QAAZ,EAAsBC,OAAtB;GADF,MAEO,IAAItD,GAAG,CAAChB,MAAJ,KAAeL,SAAnB,EAA8B;IACnC4B,CAAC,GAAG,CAAJ;;WACOA,CAAC,GAAGP,GAAG,CAAChB,MAAf,EAAuB;MACrBqE,QAAQ,CAACE,IAAT,CAAcD,OAAd,EAAuBtD,GAAG,CAACO,CAAD,CAA1B,EAA+BA,CAA/B,EAAkCP,GAAlC;MACAO,CAAC;;GAJE,MAMA;SACAA,CAAL,IAAUP,GAAV,EAAe;MACbA,GAAG,CAACb,cAAJ,CAAmBoB,CAAnB,KAAyB8C,QAAQ,CAACE,IAAT,CAAcD,OAAd,EAAuBtD,GAAG,CAACO,CAAD,CAA1B,EAA+BA,CAA/B,EAAkCP,GAAlC,CAAzB;;;;;ACvBN;;;;;;;;;AAQA,AAAe,SAASwD,QAAT,CAAkBtC,GAAlB,EAAuBuC,IAAvB,EAA6B;MACtC,OAAOvC,GAAP,KAAezB,aAAnB,EAAkC;WACzByB,GAAG,CAACwC,KAAJ,CAAUD,IAAI,GAAGA,IAAI,CAAC,CAAD,CAAJ,IAAW9E,SAAd,GAA0BA,SAAxC,EAAmD8E,IAAnD,CAAP;;;SAEKvC,GAAP;;;ACbF;;;;;;;AAOA,AAAe,SAASyC,KAAT,CAAeC,GAAf,EAAoBC,IAApB,EAA0B;SAChCD,GAAG,CAACE,OAAJ,CAAYD,IAAZ,IAAoB,CAAC,CAA5B;;;ACCF;;;;;;;AAMA,AAAe,SAASE,iBAAT,CAA2BC,OAA3B,EAAoC;;MAE7CL,KAAK,CAACK,OAAD,EAAU1C,iBAAV,CAAT,EAAuC;WAC9BA,iBAAP;;;MAGE2C,OAAO,GAAGN,KAAK,CAACK,OAAD,EAAUzC,kBAAV,CAAnB;MACI2C,OAAO,GAAGP,KAAK,CAACK,OAAD,EAAUxC,kBAAV,CAAnB,CAPiD;;;;;MAa7CyC,OAAO,IAAIC,OAAf,EAAwB;WACf5C,iBAAP;GAd+C;;;MAkB7C2C,OAAO,IAAIC,OAAf,EAAwB;WACfD,OAAO,GAAG1C,kBAAH,GAAwBC,kBAAtC;GAnB+C;;;MAuB7CmC,KAAK,CAACK,OAAD,EAAU3C,yBAAV,CAAT,EAA+C;WACtCA,yBAAP;;;SAGKD,iBAAP;;;ACtBF;;;;;;;;;IAQqB+C;;;uBACPC,OAAZ,EAAqBC,KAArB,EAA4B;SACrBD,OAAL,GAAeA,OAAf;SACKE,GAAL,CAASD,KAAT;;;;;;;;;;;SAQFC,mBAAID,OAAO;;QAELA,KAAK,KAAKlD,oBAAd,EAAoC;MAClCkD,KAAK,GAAG,KAAKE,OAAL,EAAR;;;QAGE5D,mBAAmB,IAAI,KAAKyD,OAAL,CAAaI,OAAb,CAAqBjF,KAA5C,IAAqDkC,gBAAgB,CAAC4C,KAAD,CAAzE,EAAkF;WAC3ED,OAAL,CAAaI,OAAb,CAAqBjF,KAArB,CAA2BmB,qBAA3B,IAAoD2D,KAApD;;;SAEGL,OAAL,GAAeK,KAAK,CAACI,WAAN,GAAoBC,IAApB,EAAf;;;;;;;;SAOFC,2BAAS;SACFL,GAAL,CAAS,KAAKF,OAAL,CAAaQ,OAAb,CAAqBC,WAA9B;;;;;;;;;SAQFN,6BAAU;QACJP,OAAO,GAAG,EAAd;IACAZ,IAAI,CAAC,KAAKgB,OAAL,CAAaU,WAAd,EAA2B,UAACC,UAAD,EAAgB;UACzCvB,QAAQ,CAACuB,UAAU,CAACH,OAAX,CAAmBI,MAApB,EAA4B,CAACD,UAAD,CAA5B,CAAZ,EAAuD;QACrDf,OAAO,GAAGA,OAAO,CAACiB,MAAR,CAAeF,UAAU,CAACG,cAAX,EAAf,CAAV;;KAFA,CAAJ;WAKOnB,iBAAiB,CAACC,OAAO,CAACmB,IAAR,CAAa,GAAb,CAAD,CAAxB;;;;;;;;;SAQFC,2CAAgBC,OAAO;QACfC,QADe,GACFD,KADE,CACfC,QADe;QAEjBC,SAAS,GAAGF,KAAK,CAACG,eAAtB,CAFqB;;QAKjB,KAAKpB,OAAL,CAAaqB,OAAb,CAAqBC,SAAzB,EAAoC;MAClCJ,QAAQ,CAACK,cAAT;;;;QAII3B,OAVe,GAUH,IAVG,CAUfA,OAVe;QAWjB4B,OAAO,GAAGjC,KAAK,CAACK,OAAD,EAAU1C,iBAAV,CAAL,IAAqC,CAACG,gBAAgB,CAACH,iBAAD,CAApE;QACI4C,OAAO,GAAGP,KAAK,CAACK,OAAD,EAAUxC,kBAAV,CAAL,IAAsC,CAACC,gBAAgB,CAACD,kBAAD,CAArE;QACIyC,OAAO,GAAGN,KAAK,CAACK,OAAD,EAAUzC,kBAAV,CAAL,IAAsC,CAACE,gBAAgB,CAACF,kBAAD,CAArE;;QAEIqE,OAAJ,EAAa;;UAEPC,YAAY,GAAGR,KAAK,CAACS,QAAN,CAAe9G,MAAf,KAA0B,CAA7C;UACI+G,aAAa,GAAGV,KAAK,CAACW,QAAN,GAAiB,CAArC;UACIC,cAAc,GAAGZ,KAAK,CAACa,SAAN,GAAkB,GAAvC;;UAEIL,YAAY,IAAIE,aAAhB,IAAiCE,cAArC,EAAqD;;;;;QAKnDhC,OAAO,IAAIC,OAAf,EAAwB;;;;;QAKpB0B,OAAO,IACN1B,OAAO,IAAIqB,SAAS,GAAGxC,oBADxB,IAECkB,OAAO,IAAIsB,SAAS,GAAGvC,kBAF5B,EAEiD;aACxC,KAAKmD,UAAL,CAAgBb,QAAhB,CAAP;;;;;;;;;;SASJa,iCAAWb,UAAU;SACdlB,OAAL,CAAaqB,OAAb,CAAqBC,SAArB,GAAiC,IAAjC;IACAJ,QAAQ,CAACK,cAAT;;;;;;AC5HJ;;;;;;;;AAQA,AAAe,SAASS,SAAT,CAAmBC,IAAnB,EAAyBC,MAAzB,EAAiC;SACvCD,IAAP,EAAa;QACPA,IAAI,KAAKC,MAAb,EAAqB;aACZ,IAAP;;;IAEFD,IAAI,GAAGA,IAAI,CAACE,UAAZ;;;SAEK,KAAP;;;ACbF;;;;;;;AAMA,AAAe,SAASC,SAAT,CAAmBV,QAAnB,EAA6B;MACtCW,cAAc,GAAGX,QAAQ,CAAC9G,MAA9B,CAD0C;;MAItCyH,cAAc,KAAK,CAAvB,EAA0B;WACjB;MACLC,CAAC,EAAEhH,KAAK,CAACoG,QAAQ,CAAC,CAAD,CAAR,CAAYa,OAAb,CADH;MAELC,CAAC,EAAElH,KAAK,CAACoG,QAAQ,CAAC,CAAD,CAAR,CAAYe,OAAb;KAFV;;;MAMEH,CAAC,GAAG,CAAR;MACIE,CAAC,GAAG,CAAR;MACIrG,CAAC,GAAG,CAAR;;SACOA,CAAC,GAAGkG,cAAX,EAA2B;IACzBC,CAAC,IAAIZ,QAAQ,CAACvF,CAAD,CAAR,CAAYoG,OAAjB;IACAC,CAAC,IAAId,QAAQ,CAACvF,CAAD,CAAR,CAAYsG,OAAjB;IACAtG,CAAC;;;SAGI;IACLmG,CAAC,EAAEhH,KAAK,CAACgH,CAAC,GAAGD,cAAL,CADH;IAELG,CAAC,EAAElH,KAAK,CAACkH,CAAC,GAAGH,cAAL;GAFV;;;ACzBF;;;;;;;AAMA,AAAe,SAASK,oBAAT,CAA8BzB,KAA9B,EAAqC;;;MAG9CS,QAAQ,GAAG,EAAf;MACIvF,CAAC,GAAG,CAAR;;SACOA,CAAC,GAAG8E,KAAK,CAACS,QAAN,CAAe9G,MAA1B,EAAkC;IAChC8G,QAAQ,CAACvF,CAAD,CAAR,GAAc;MACZoG,OAAO,EAAEjH,KAAK,CAAC2F,KAAK,CAACS,QAAN,CAAevF,CAAf,EAAkBoG,OAAnB,CADF;MAEZE,OAAO,EAAEnH,KAAK,CAAC2F,KAAK,CAACS,QAAN,CAAevF,CAAf,EAAkBsG,OAAnB;KAFhB;IAIAtG,CAAC;;;SAGI;IACLwG,SAAS,EAAElH,GAAG,EADT;IAELiG,QAAQ,EAARA,QAFK;IAGLkB,MAAM,EAAER,SAAS,CAACV,QAAD,CAHZ;IAILmB,MAAM,EAAE5B,KAAK,CAAC4B,MAJT;IAKLC,MAAM,EAAE7B,KAAK,CAAC6B;GALhB;;;ACpBF;;;;;;;;;AAQA,AAAe,SAASC,WAAT,CAAqBC,EAArB,EAAyBC,EAAzB,EAA6BC,KAA7B,EAAoC;MAC7C,CAACA,KAAL,EAAY;IACVA,KAAK,GAAGpE,QAAR;;;MAEEwD,CAAC,GAAGW,EAAE,CAACC,KAAK,CAAC,CAAD,CAAN,CAAF,GAAeF,EAAE,CAACE,KAAK,CAAC,CAAD,CAAN,CAAzB;MACIV,CAAC,GAAGS,EAAE,CAACC,KAAK,CAAC,CAAD,CAAN,CAAF,GAAeF,EAAE,CAACE,KAAK,CAAC,CAAD,CAAN,CAAzB;SAEO3H,IAAI,CAAC4H,IAAL,CAAWb,CAAC,GAAGA,CAAL,GAAWE,CAAC,GAAGA,CAAzB,CAAP;;;ACfF;;;;;;;;;AAQA,AAAe,SAASY,QAAT,CAAkBJ,EAAlB,EAAsBC,EAAtB,EAA0BC,KAA1B,EAAiC;MAC1C,CAACA,KAAL,EAAY;IACVA,KAAK,GAAGpE,QAAR;;;MAEEwD,CAAC,GAAGW,EAAE,CAACC,KAAK,CAAC,CAAD,CAAN,CAAF,GAAeF,EAAE,CAACE,KAAK,CAAC,CAAD,CAAN,CAAzB;MACIV,CAAC,GAAGS,EAAE,CAACC,KAAK,CAAC,CAAD,CAAN,CAAF,GAAeF,EAAE,CAACE,KAAK,CAAC,CAAD,CAAN,CAAzB;SACO3H,IAAI,CAAC8H,KAAL,CAAWb,CAAX,EAAcF,CAAd,IAAmB,GAAnB,GAAyB/G,IAAI,CAAC+H,EAArC;;;ACbF;;;;;;;;AAOA,AAAe,SAASC,YAAT,CAAsBjB,CAAtB,EAAyBE,CAAzB,EAA4B;MACrCF,CAAC,KAAKE,CAAV,EAAa;WACJlE,cAAP;;;MAGE9C,GAAG,CAAC8G,CAAD,CAAH,IAAU9G,GAAG,CAACgH,CAAD,CAAjB,EAAsB;WACbF,CAAC,GAAG,CAAJ,GAAQ/D,cAAR,GAAyBC,eAAhC;;;SAEKgE,CAAC,GAAG,CAAJ,GAAQ/D,YAAR,GAAuBC,cAA9B;;;AChBa,SAAS8E,cAAT,CAAwBnC,OAAxB,EAAiCJ,KAAjC,EAAwC;MAC/C2B,MAD+C,GACpC3B,KADoC,CAC/C2B,MAD+C;;;MAIjDa,MAAM,GAAGpC,OAAO,CAACqC,WAAR,IAAuB,EAApC;MACIC,SAAS,GAAGtC,OAAO,CAACsC,SAAR,IAAqB,EAArC;MACIC,SAAS,GAAGvC,OAAO,CAACuC,SAAR,IAAqB,EAArC;;MAEI3C,KAAK,CAAC4C,SAAN,KAAoB3F,WAApB,IAAmC0F,SAAS,CAACC,SAAV,KAAwBzF,SAA/D,EAA0E;IACxEuF,SAAS,GAAGtC,OAAO,CAACsC,SAAR,GAAoB;MAC9BrB,CAAC,EAAEsB,SAAS,CAACf,MAAV,IAAoB,CADO;MAE9BL,CAAC,EAAEoB,SAAS,CAACd,MAAV,IAAoB;KAFzB;IAKAW,MAAM,GAAGpC,OAAO,CAACqC,WAAR,GAAsB;MAC7BpB,CAAC,EAAEM,MAAM,CAACN,CADmB;MAE7BE,CAAC,EAAEI,MAAM,CAACJ;KAFZ;;;EAMFvB,KAAK,CAAC4B,MAAN,GAAec,SAAS,CAACrB,CAAV,IAAeM,MAAM,CAACN,CAAP,GAAWmB,MAAM,CAACnB,CAAjC,CAAf;EACArB,KAAK,CAAC6B,MAAN,GAAea,SAAS,CAACnB,CAAV,IAAeI,MAAM,CAACJ,CAAP,GAAWiB,MAAM,CAACjB,CAAjC,CAAf;;;ACvBF;;;;;;;;AAQA,AAAe,SAASsB,WAAT,CAAqBhC,SAArB,EAAgCQ,CAAhC,EAAmCE,CAAnC,EAAsC;SAC5C;IACLF,CAAC,EAAEA,CAAC,GAAGR,SAAJ,IAAiB,CADf;IAELU,CAAC,EAAEA,CAAC,GAAGV,SAAJ,IAAiB;GAFtB;;;ACPF;;;;;;;;;AAQA,AAAe,SAASiC,QAAT,CAAkBC,KAAlB,EAAyBC,GAAzB,EAA8B;SACpClB,WAAW,CAACkB,GAAG,CAAC,CAAD,CAAJ,EAASA,GAAG,CAAC,CAAD,CAAZ,EAAiBlF,eAAjB,CAAX,GAA+CgE,WAAW,CAACiB,KAAK,CAAC,CAAD,CAAN,EAAWA,KAAK,CAAC,CAAD,CAAhB,EAAqBjF,eAArB,CAAjE;;;ACRF;;;;;;;;AAOA,AAAe,SAASmF,WAAT,CAAqBF,KAArB,EAA4BC,GAA5B,EAAiC;SACvCb,QAAQ,CAACa,GAAG,CAAC,CAAD,CAAJ,EAASA,GAAG,CAAC,CAAD,CAAZ,EAAiBlF,eAAjB,CAAR,GAA4CqE,QAAQ,CAACY,KAAK,CAAC,CAAD,CAAN,EAAWA,KAAK,CAAC,CAAD,CAAhB,EAAqBjF,eAArB,CAA3D;;;ACNF;;;;;;;AAMA,AAAe,SAASoF,wBAAT,CAAkC9C,OAAlC,EAA2CJ,KAA3C,EAAkD;MAC3DmD,IAAI,GAAG/C,OAAO,CAACgD,YAAR,IAAwBpD,KAAnC;MACIa,SAAS,GAAGb,KAAK,CAAC0B,SAAN,GAAkByB,IAAI,CAACzB,SAAvC;MACI2B,QAAJ;MACIC,SAAJ;MACIC,SAAJ;MACIrD,SAAJ;;MAEIF,KAAK,CAAC4C,SAAN,KAAoBxF,YAApB,KAAqCyD,SAAS,GAAG7D,gBAAZ,IAAgCmG,IAAI,CAACE,QAAL,KAAkB/J,SAAvF,CAAJ,EAAuG;QACjGsI,MAAM,GAAG5B,KAAK,CAAC4B,MAAN,GAAeuB,IAAI,CAACvB,MAAjC;QACIC,MAAM,GAAG7B,KAAK,CAAC6B,MAAN,GAAesB,IAAI,CAACtB,MAAjC;QAEI2B,CAAC,GAAGX,WAAW,CAAChC,SAAD,EAAYe,MAAZ,EAAoBC,MAApB,CAAnB;IACAyB,SAAS,GAAGE,CAAC,CAACnC,CAAd;IACAkC,SAAS,GAAGC,CAAC,CAACjC,CAAd;IACA8B,QAAQ,GAAI9I,GAAG,CAACiJ,CAAC,CAACnC,CAAH,CAAH,GAAW9G,GAAG,CAACiJ,CAAC,CAACjC,CAAH,CAAf,GAAwBiC,CAAC,CAACnC,CAA1B,GAA8BmC,CAAC,CAACjC,CAA3C;IACArB,SAAS,GAAGoC,YAAY,CAACV,MAAD,EAASC,MAAT,CAAxB;IAEAzB,OAAO,CAACgD,YAAR,GAAuBpD,KAAvB;GAVF,MAWO;;IAELqD,QAAQ,GAAGF,IAAI,CAACE,QAAhB;IACAC,SAAS,GAAGH,IAAI,CAACG,SAAjB;IACAC,SAAS,GAAGJ,IAAI,CAACI,SAAjB;IACArD,SAAS,GAAGiD,IAAI,CAACjD,SAAjB;;;EAGFF,KAAK,CAACqD,QAAN,GAAiBA,QAAjB;EACArD,KAAK,CAACsD,SAAN,GAAkBA,SAAlB;EACAtD,KAAK,CAACuD,SAAN,GAAkBA,SAAlB;EACAvD,KAAK,CAACE,SAAN,GAAkBA,SAAlB;;;AC3BF;;;;;;;AAMA,AAAe,SAASuD,gBAAT,CAA0B1E,OAA1B,EAAmCiB,KAAnC,EAA0C;MACjDI,OADiD,GACrCrB,OADqC,CACjDqB,OADiD;MAEjDK,QAFiD,GAEpCT,KAFoC,CAEjDS,QAFiD;MAG1CW,cAH0C,GAGvBX,QAHuB,CAGjD9G,MAHiD;;MAMnD,CAACyG,OAAO,CAACsD,UAAb,EAAyB;IACvBtD,OAAO,CAACsD,UAAR,GAAqBjC,oBAAoB,CAACzB,KAAD,CAAzC;GAPqD;;;MAWnDoB,cAAc,GAAG,CAAjB,IAAsB,CAAChB,OAAO,CAACuD,aAAnC,EAAkD;IAChDvD,OAAO,CAACuD,aAAR,GAAwBlC,oBAAoB,CAACzB,KAAD,CAA5C;GADF,MAEO,IAAIoB,cAAc,KAAK,CAAvB,EAA0B;IAC/BhB,OAAO,CAACuD,aAAR,GAAwB,KAAxB;;;MAGID,UAjBiD,GAiBnBtD,OAjBmB,CAiBjDsD,UAjBiD;MAiBrCC,aAjBqC,GAiBnBvD,OAjBmB,CAiBrCuD,aAjBqC;MAkBnDC,YAAY,GAAGD,aAAa,GAAGA,aAAa,CAAChC,MAAjB,GAA0B+B,UAAU,CAAC/B,MAArE;MAEIA,MAAM,GAAG3B,KAAK,CAAC2B,MAAN,GAAeR,SAAS,CAACV,QAAD,CAArC;EACAT,KAAK,CAAC0B,SAAN,GAAkBlH,GAAG,EAArB;EACAwF,KAAK,CAACa,SAAN,GAAkBb,KAAK,CAAC0B,SAAN,GAAkBgC,UAAU,CAAChC,SAA/C;EAEA1B,KAAK,CAAC6D,KAAN,GAAc1B,QAAQ,CAACyB,YAAD,EAAejC,MAAf,CAAtB;EACA3B,KAAK,CAACW,QAAN,GAAiBmB,WAAW,CAAC8B,YAAD,EAAejC,MAAf,CAA5B;EAEAY,cAAc,CAACnC,OAAD,EAAUJ,KAAV,CAAd;EACAA,KAAK,CAACG,eAAN,GAAwBmC,YAAY,CAACtC,KAAK,CAAC4B,MAAP,EAAe5B,KAAK,CAAC6B,MAArB,CAApC;MAEIiC,eAAe,GAAGjB,WAAW,CAAC7C,KAAK,CAACa,SAAP,EAAkBb,KAAK,CAAC4B,MAAxB,EAAgC5B,KAAK,CAAC6B,MAAtC,CAAjC;EACA7B,KAAK,CAAC+D,gBAAN,GAAyBD,eAAe,CAACzC,CAAzC;EACArB,KAAK,CAACgE,gBAAN,GAAyBF,eAAe,CAACvC,CAAzC;EACAvB,KAAK,CAAC8D,eAAN,GAAyBvJ,GAAG,CAACuJ,eAAe,CAACzC,CAAjB,CAAH,GAAyB9G,GAAG,CAACuJ,eAAe,CAACvC,CAAjB,CAA7B,GAAoDuC,eAAe,CAACzC,CAApE,GAAwEyC,eAAe,CAACvC,CAAhH;EAEAvB,KAAK,CAACiE,KAAN,GAAcN,aAAa,GAAGb,QAAQ,CAACa,aAAa,CAAClD,QAAf,EAAyBA,QAAzB,CAAX,GAAgD,CAA3E;EACAT,KAAK,CAACkE,QAAN,GAAiBP,aAAa,GAAGV,WAAW,CAACU,aAAa,CAAClD,QAAf,EAAyBA,QAAzB,CAAd,GAAmD,CAAjF;EAEAT,KAAK,CAACmE,WAAN,GAAoB,CAAC/D,OAAO,CAACuC,SAAT,GAAqB3C,KAAK,CAACS,QAAN,CAAe9G,MAApC,GAA+CqG,KAAK,CAACS,QAAN,CAAe9G,MAAf,GACnEyG,OAAO,CAACuC,SAAR,CAAkBwB,WADgD,GACjCnE,KAAK,CAACS,QAAN,CAAe9G,MADkB,GACTyG,OAAO,CAACuC,SAAR,CAAkBwB,WAD3E;EAGAjB,wBAAwB,CAAC9C,OAAD,EAAUJ,KAAV,CAAxB,CAzCuD;;MA4CnD3G,MAAM,GAAG0F,OAAO,CAACI,OAArB;MACMc,QAAQ,GAAGD,KAAK,CAACC,QAAvB;MACImE,cAAJ;;MAEInE,QAAQ,CAACoE,YAAb,EAA2B;IACzBD,cAAc,GAAGnE,QAAQ,CAACoE,YAAT,GAAwB,CAAxB,CAAjB;GADF,MAEO,IAAIpE,QAAQ,CAACqE,IAAb,EAAmB;IACxBF,cAAc,GAAGnE,QAAQ,CAACqE,IAAT,CAAc,CAAd,CAAjB;GADK,MAEA;IACLF,cAAc,GAAGnE,QAAQ,CAAC5G,MAA1B;;;MAGE0H,SAAS,CAACqD,cAAD,EAAiB/K,MAAjB,CAAb,EAAuC;IACrCA,MAAM,GAAG+K,cAAT;;;EAEFpE,KAAK,CAAC3G,MAAN,GAAeA,MAAf;;;AC5EF;;;;;;;;AAOA,AAAe,SAASkL,YAAT,CAAsBxF,OAAtB,EAA+B6D,SAA/B,EAA0C5C,KAA1C,EAAiD;MAC1DwE,WAAW,GAAGxE,KAAK,CAACS,QAAN,CAAe9G,MAAjC;MACI8K,kBAAkB,GAAGzE,KAAK,CAAC0E,eAAN,CAAsB/K,MAA/C;MACIgL,OAAO,GAAI/B,SAAS,GAAG3F,WAAZ,IAA4BuH,WAAW,GAAGC,kBAAd,KAAqC,CAAhF;MACIG,OAAO,GAAIhC,SAAS,IAAIzF,SAAS,GAAGC,YAAhB,CAAT,IAA2CoH,WAAW,GAAGC,kBAAd,KAAqC,CAA/F;EAEAzE,KAAK,CAAC2E,OAAN,GAAgB,CAAC,CAACA,OAAlB;EACA3E,KAAK,CAAC4E,OAAN,GAAgB,CAAC,CAACA,OAAlB;;MAEID,OAAJ,EAAa;IACX5F,OAAO,CAACqB,OAAR,GAAkB,EAAlB;GAV4D;;;;EAe9DJ,KAAK,CAAC4C,SAAN,GAAkBA,SAAlB,CAf8D;;EAkB9Da,gBAAgB,CAAC1E,OAAD,EAAUiB,KAAV,CAAhB,CAlB8D;;EAqB9DjB,OAAO,CAAC8F,IAAR,CAAa,cAAb,EAA6B7E,KAA7B;EAEAjB,OAAO,CAAC+F,SAAR,CAAkB9E,KAAlB;EACAjB,OAAO,CAACqB,OAAR,CAAgBuC,SAAhB,GAA4B3C,KAA5B;;;AClCF;;;;;;AAOA,AAAe,SAAS+E,QAAT,CAAkBxG,GAAlB,EAAuB;SAC7BA,GAAG,CAACc,IAAJ,GAAW2F,KAAX,CAAiB,MAAjB,CAAP;;;ACNF;;;;;;;;AAOA,AAAe,SAASC,iBAAT,CAA2B5L,MAA3B,EAAmC6L,KAAnC,EAA0CC,OAA1C,EAAmD;EAChEpH,IAAI,CAACgH,QAAQ,CAACG,KAAD,CAAT,EAAkB,UAACE,IAAD,EAAU;IAC9B/L,MAAM,CAACgM,gBAAP,CAAwBD,IAAxB,EAA8BD,OAA9B,EAAuC,KAAvC;GADE,CAAJ;;;ACRF;;;;;;;;AAOA,AAAe,SAASG,oBAAT,CAA8BjM,MAA9B,EAAsC6L,KAAtC,EAA6CC,OAA7C,EAAsD;EACnEpH,IAAI,CAACgH,QAAQ,CAACG,KAAD,CAAT,EAAkB,UAACE,IAAD,EAAU;IAC9B/L,MAAM,CAACkM,mBAAP,CAA2BH,IAA3B,EAAiCD,OAAjC,EAA0C,KAA1C;GADE,CAAJ;;;ACVF;;;;;;AAMA,AAAe,SAASK,mBAAT,CAA6BrG,OAA7B,EAAsC;MAC/CsG,GAAG,GAAGtG,OAAO,CAACuG,aAAR,IAAyBvG,OAAnC;SACQsG,GAAG,CAACE,WAAJ,IAAmBF,GAAG,CAACG,YAAvB,IAAuCxK,MAA/C;;;ACHF;;;;;;;;;IAQqByK;;;iBACP9G,OAAZ,EAAqB+G,QAArB,EAA+B;QACzBC,IAAI,GAAG,IAAX;SACKhH,OAAL,GAAeA,OAAf;SACK+G,QAAL,GAAgBA,QAAhB;SACK3G,OAAL,GAAeJ,OAAO,CAACI,OAAvB;SACK9F,MAAL,GAAc0F,OAAO,CAACQ,OAAR,CAAgByG,WAA9B,CAL6B;;;SASxBC,UAAL,GAAkB,UAASC,EAAT,EAAa;UACzB/H,QAAQ,CAACY,OAAO,CAACQ,OAAR,CAAgBI,MAAjB,EAAyB,CAACZ,OAAD,CAAzB,CAAZ,EAAiD;QAC/CgH,IAAI,CAACZ,OAAL,CAAae,EAAb;;KAFJ;;SAMKC,IAAL;;;;;;;;;;;SAQFhB,6BAAU;;;;;;;SAMVgB,uBAAO;SACAC,IAAL,IAAanB,iBAAiB,CAAC,KAAK9F,OAAN,EAAe,KAAKiH,IAApB,EAA0B,KAAKH,UAA/B,CAA9B;SACKI,QAAL,IAAiBpB,iBAAiB,CAAC,KAAK5L,MAAN,EAAc,KAAKgN,QAAnB,EAA6B,KAAKJ,UAAlC,CAAlC;SACKK,KAAL,IAAcrB,iBAAiB,CAACO,mBAAmB,CAAC,KAAKrG,OAAN,CAApB,EAAoC,KAAKmH,KAAzC,EAAgD,KAAKL,UAArD,CAA/B;;;;;;;;SAOFM,6BAAU;SACHH,IAAL,IAAad,oBAAoB,CAAC,KAAKnG,OAAN,EAAe,KAAKiH,IAApB,EAA0B,KAAKH,UAA/B,CAAjC;SACKI,QAAL,IAAiBf,oBAAoB,CAAC,KAAKjM,MAAN,EAAc,KAAKgN,QAAnB,EAA6B,KAAKJ,UAAlC,CAArC;SACKK,KAAL,IAAchB,oBAAoB,CAACE,mBAAmB,CAAC,KAAKrG,OAAN,CAApB,EAAoC,KAAKmH,KAAzC,EAAgD,KAAKL,UAArD,CAAlC;;;;;;ACxDJ;;;;;;;;AAQA,AAAe,SAASO,OAAT,CAAiBC,GAAjB,EAAsBjI,IAAtB,EAA4BkI,SAA5B,EAAuC;MAChDD,GAAG,CAAChI,OAAJ,IAAe,CAACiI,SAApB,EAA+B;WACtBD,GAAG,CAAChI,OAAJ,CAAYD,IAAZ,CAAP;GADF,MAEO;QACDtD,CAAC,GAAG,CAAR;;WACOA,CAAC,GAAGuL,GAAG,CAAC9M,MAAf,EAAuB;UAChB+M,SAAS,IAAID,GAAG,CAACvL,CAAD,CAAH,CAAOwL,SAAP,KAAqBlI,IAAnC,IAA6C,CAACkI,SAAD,IAAcD,GAAG,CAACvL,CAAD,CAAH,KAAWsD,IAA1E,EAAiF;;eACxEtD,CAAP;;;MAEFA,CAAC;;;WAEI,CAAC,CAAR;;;;ACLJ,IAAMyL,iBAAiB,GAAG;EACxBC,WAAW,EAAE3J,WADW;EAExB4J,WAAW,EAAE3J,UAFW;EAGxB4J,SAAS,EAAE3J,SAHa;EAIxB4J,aAAa,EAAE3J,YAJS;EAKxB4J,UAAU,EAAE5J;CALd;;AASA,IAAM6J,sBAAsB,GAAG;KAC1BrK,gBAD0B;KAE1BC,cAF0B;KAG1BC,gBAH0B;KAI1BC,iBAJ0B;;CAA/B;AAOA,IAAImK,sBAAsB,GAAG,aAA7B;AACA,IAAIC,qBAAqB,GAAG,qCAA5B;;AAGA,IAAI/L,GAAM,CAACgM,cAAP,IAAyB,CAAChM,GAAM,CAACiM,YAArC,EAAmD;EACjDH,sBAAsB,GAAG,eAAzB;EACAC,qBAAqB,GAAG,2CAAxB;;;;;;;;;;IASmBG;;;;;+BACL;;;QACRC,KAAK,GAAGD,iBAAiB,CAACE,SAA9B;IAEAD,KAAK,CAACnB,IAAN,GAAac,sBAAb;IACAK,KAAK,CAACjB,KAAN,GAAca,qBAAd;+BACSzN,SAAT;UACK+N,KAAL,GAAc,MAAK1I,OAAL,CAAaqB,OAAb,CAAqBsH,aAArB,GAAqC,EAAnD;;;;;;;;;;;;SAQFvC,2BAAQe,IAAI;QACJuB,KADI,GACM,IADN,CACJA,KADI;QAENE,aAAa,GAAG,KAApB;QAEIC,mBAAmB,GAAG1B,EAAE,CAACd,IAAH,CAAQhG,WAAR,GAAsByI,OAAtB,CAA8B,IAA9B,EAAoC,EAApC,CAA1B;QACIjF,SAAS,GAAG+D,iBAAiB,CAACiB,mBAAD,CAAjC;QACIE,WAAW,GAAGb,sBAAsB,CAACf,EAAE,CAAC4B,WAAJ,CAAtB,IAA0C5B,EAAE,CAAC4B,WAA/D;QAEIC,OAAO,GAAID,WAAW,KAAKlL,gBAA/B,CARU;;QAWNoL,UAAU,GAAGxB,OAAO,CAACiB,KAAD,EAAQvB,EAAE,CAAC+B,SAAX,EAAsB,WAAtB,CAAxB,CAXU;;QAcNrF,SAAS,GAAG3F,WAAZ,KAA4BiJ,EAAE,CAACgC,MAAH,KAAc,CAAd,IAAmBH,OAA/C,CAAJ,EAA6D;UACvDC,UAAU,GAAG,CAAjB,EAAoB;QAClBP,KAAK,CAACU,IAAN,CAAWjC,EAAX;QACA8B,UAAU,GAAGP,KAAK,CAAC9N,MAAN,GAAe,CAA5B;;KAHJ,MAKO,IAAIiJ,SAAS,IAAIzF,SAAS,GAAGC,YAAhB,CAAb,EAA4C;MACjDuK,aAAa,GAAG,IAAhB;KApBQ;;;QAwBNK,UAAU,GAAG,CAAjB,EAAoB;;KAxBV;;;IA6BVP,KAAK,CAACO,UAAD,CAAL,GAAoB9B,EAApB;SAEKJ,QAAL,CAAc,KAAK/G,OAAnB,EAA4B6D,SAA5B,EAAuC;MACrCnC,QAAQ,EAAEgH,KAD2B;MAErC/C,eAAe,EAAE,CAACwB,EAAD,CAFoB;MAGrC4B,WAAW,EAAXA,WAHqC;MAIrC7H,QAAQ,EAAEiG;KAJZ;;QAOIyB,aAAJ,EAAmB;;MAEjBF,KAAK,CAACW,MAAN,CAAaJ,UAAb,EAAyB,CAAzB;;;;;EAvDyCnC;;AC7C/C;;;;;;AAMA,AAAe,SAASwC,OAAT,CAAiB1N,GAAjB,EAAsB;SAC5B2N,KAAK,CAACd,SAAN,CAAgBvM,KAAhB,CAAsBiD,IAAtB,CAA2BvD,GAA3B,EAAgC,CAAhC,CAAP;;;ACLF;;;;;;;;;AAQA,AAAe,SAAS4N,WAAT,CAAqB9B,GAArB,EAA0B+B,GAA1B,EAA+BC,IAA/B,EAAqC;MAC9CC,OAAO,GAAG,EAAd;MACIC,MAAM,GAAG,EAAb;MACIzN,CAAC,GAAG,CAAR;;SAEOA,CAAC,GAAGuL,GAAG,CAAC9M,MAAf,EAAuB;QACjBkC,GAAG,GAAG2M,GAAG,GAAG/B,GAAG,CAACvL,CAAD,CAAH,CAAOsN,GAAP,CAAH,GAAiB/B,GAAG,CAACvL,CAAD,CAAjC;;QACIsL,OAAO,CAACmC,MAAD,EAAS9M,GAAT,CAAP,GAAuB,CAA3B,EAA8B;MAC5B6M,OAAO,CAACP,IAAR,CAAa1B,GAAG,CAACvL,CAAD,CAAhB;;;IAEFyN,MAAM,CAACzN,CAAD,CAAN,GAAYW,GAAZ;IACAX,CAAC;;;MAGCuN,IAAJ,EAAU;QACJ,CAACD,GAAL,EAAU;MACRE,OAAO,GAAGA,OAAO,CAACD,IAAR,EAAV;KADF,MAEO;MACLC,OAAO,GAAGA,OAAO,CAACD,IAAR,CAAa,UAACG,CAAD,EAAIC,CAAJ,EAAU;eACxBD,CAAC,CAACJ,GAAD,CAAD,GAASK,CAAC,CAACL,GAAD,CAAjB;OADQ,CAAV;;;;SAMGE,OAAP;;;ACtBF,IAAMI,eAAe,GAAG;EACtBC,UAAU,EAAE9L,WADU;EAEtB+L,SAAS,EAAE9L,UAFW;EAGtB+L,QAAQ,EAAE9L,SAHY;EAItB+L,WAAW,EAAE9L;CAJf;AAOA,IAAM+L,mBAAmB,GAAG,2CAA5B;;;;;;;;IAQqBC;;;;;wBACL;;;IACZA,UAAU,CAAC5B,SAAX,CAAqBnB,QAArB,GAAgC8C,mBAAhC;+BACSzP,SAAT;UACK2P,SAAL,GAAiB,EAAjB,CAHY;;;;;;;SAMdlE,2BAAQe,IAAI;QACNd,IAAI,GAAG0D,eAAe,CAAC5C,EAAE,CAACd,IAAJ,CAA1B;QACIkE,OAAO,GAAGC,UAAU,CAACrL,IAAX,CAAgB,IAAhB,EAAsBgI,EAAtB,EAA0Bd,IAA1B,CAAd;;QACI,CAACkE,OAAL,EAAc;;;;SAITxD,QAAL,CAAc,KAAK/G,OAAnB,EAA4BqG,IAA5B,EAAkC;MAChC3E,QAAQ,EAAE6I,OAAO,CAAC,CAAD,CADe;MAEhC5E,eAAe,EAAE4E,OAAO,CAAC,CAAD,CAFQ;MAGhCxB,WAAW,EAAElL,gBAHmB;MAIhCqD,QAAQ,EAAEiG;KAJZ;;;;EAdoCL;AAuBxC;AAOA,SAAS0D,UAAT,CAAoBrD,EAApB,EAAwBd,IAAxB,EAA8B;MACxBoE,UAAU,GAAGnB,OAAO,CAACnC,EAAE,CAACoD,OAAJ,CAAxB;MACMD,SAFsB,GAER,IAFQ,CAEtBA,SAFsB;;MAKxBjE,IAAI,IAAInI,WAAW,GAAGC,UAAlB,CAAJ,IAAqCsM,UAAU,CAAC7P,MAAX,KAAsB,CAA/D,EAAkE;IAChE0P,SAAS,CAACG,UAAU,CAAC,CAAD,CAAV,CAAcC,UAAf,CAAT,GAAsC,IAAtC;WACO,CAACD,UAAD,EAAaA,UAAb,CAAP;;;MAGEtO,CAAJ;MACIwO,aAAJ;MACIC,cAAc,GAAGtB,OAAO,CAACnC,EAAE,CAACyD,cAAJ,CAA5B;MACIC,oBAAoB,GAAG,EAA3B;MACMvQ,MAdsB,GAcX,IAdW,CActBA,MAdsB;;EAiB5BqQ,aAAa,GAAGF,UAAU,CAACK,MAAX,CAAkB,UAACC,KAAD,EAAW;WACpC/I,SAAS,CAAC+I,KAAK,CAACzQ,MAAP,EAAeA,MAAf,CAAhB;GADc,CAAhB,CAjB4B;;MAsBxB+L,IAAI,KAAKnI,WAAb,EAA0B;IACxB/B,CAAC,GAAG,CAAJ;;WACOA,CAAC,GAAGwO,aAAa,CAAC/P,MAAzB,EAAiC;MAC/B0P,SAAS,CAACK,aAAa,CAACxO,CAAD,CAAb,CAAiBuO,UAAlB,CAAT,GAAyC,IAAzC;MACAvO,CAAC;;GA1BuB;;;EA+B5BA,CAAC,GAAG,CAAJ;;SACOA,CAAC,GAAGyO,cAAc,CAAChQ,MAA1B,EAAkC;QAC5B0P,SAAS,CAACM,cAAc,CAACzO,CAAD,CAAd,CAAkBuO,UAAnB,CAAb,EAA6C;MAC3CG,oBAAoB,CAACzB,IAArB,CAA0BwB,cAAc,CAACzO,CAAD,CAAxC;KAF8B;;;QAM5BkK,IAAI,IAAIjI,SAAS,GAAGC,YAAhB,CAAR,EAAuC;aAC9BiM,SAAS,CAACM,cAAc,CAACzO,CAAD,CAAd,CAAkBuO,UAAnB,CAAhB;;;IAEFvO,CAAC;;;MAGC,CAAC0O,oBAAoB,CAACjQ,MAA1B,EAAkC;;;;SAI3B;EAEL4O,WAAW,CAACmB,aAAa,CAAC9J,MAAd,CAAqBgK,oBAArB,CAAD,EAA6C,YAA7C,EAA2D,IAA3D,CAFN,EAGLA,oBAHK,CAAP;;;ACjGF,IAAMG,eAAe,GAAG;EACtBC,SAAS,EAAE/M,WADW;EAEtBgN,SAAS,EAAE/M,UAFW;EAGtBgN,OAAO,EAAE/M;CAHX;AAMA,IAAMgN,oBAAoB,GAAG,WAA7B;AACA,IAAMC,mBAAmB,GAAG,mBAA5B;;;;;;;;IAQqBC;;;;;wBACL;;;QACR9C,KAAK,GAAG8C,UAAU,CAAC7C,SAAvB;IACAD,KAAK,CAACnB,IAAN,GAAa+D,oBAAb;IACA5C,KAAK,CAACjB,KAAN,GAAc8D,mBAAd;+BAES1Q,SAAT;UACK4Q,OAAL,GAAe,KAAf,CANY;;;;;;;;;;;;;SAcdnF,2BAAQe,IAAI;QACNtD,SAAS,GAAGmH,eAAe,CAAC7D,EAAE,CAACd,IAAJ,CAA/B,CADU;;QAINxC,SAAS,GAAG3F,WAAZ,IAA2BiJ,EAAE,CAACgC,MAAH,KAAc,CAA7C,EAAgD;WACzCoC,OAAL,GAAe,IAAf;;;QAGE1H,SAAS,GAAG1F,UAAZ,IAA0BgJ,EAAE,CAACqE,KAAH,KAAa,CAA3C,EAA8C;MAC5C3H,SAAS,GAAGzF,SAAZ;KATQ;;;QAaN,CAAC,KAAKmN,OAAV,EAAmB;;;;QAIf1H,SAAS,GAAGzF,SAAhB,EAA2B;WACpBmN,OAAL,GAAe,KAAf;;;SAGGxE,QAAL,CAAc,KAAK/G,OAAnB,EAA4B6D,SAA5B,EAAuC;MACrCnC,QAAQ,EAAE,CAACyF,EAAD,CAD2B;MAErCxB,eAAe,EAAE,CAACwB,EAAD,CAFoB;MAGrC4B,WAAW,EAAEhL,gBAHwB;MAIrCmD,QAAQ,EAAEiG;KAJZ;;;;EApCoCL;;ACZxC;;;;;;;;;;;AAWA,IAAM2E,aAAa,GAAG,IAAtB;AACA,IAAMC,cAAc,GAAG,EAAvB;;AAEA,SAASC,YAAT,CAAsBC,SAAtB,EAAiC;8BACKA,SADL,CACxBjG,eADwB;MACNoF,KADM;;MAG5BA,KAAK,CAACL,UAAN,KAAqB,KAAKmB,YAA9B,EAA4C;QACrCC,SAAS,GAAG;MAAExJ,CAAC,EAAEyI,KAAK,CAACxI,OAAX;MAAoBC,CAAC,EAAEuI,KAAK,CAACtI;KAA/C;QACMsJ,GAAG,GAAG,KAAKC,WAAjB;SAEKA,WAAL,CAAiB5C,IAAjB,CAAsB0C,SAAtB;;QAGMG,eAAe,GAAG,SAAlBA,eAAkB,GAAW;UAC5B9P,CAAC,GAAG4P,GAAG,CAACrM,OAAJ,CAAYoM,SAAZ,CAAV;;UAEI3P,CAAC,GAAG,CAAC,CAAT,EAAY;QACX4P,GAAG,CAAC1C,MAAJ,CAAWlN,CAAX,EAAc,CAAd;;KAJF;;IAQA+P,UAAU,CAACD,eAAD,EAAkBR,aAAlB,CAAV;;;;AAKF,SAASU,aAAT,CAAuBtI,SAAvB,EAAkC+H,SAAlC,EAA6C;MACxC/H,SAAS,GAAG3F,WAAhB,EAA6B;SACvB2N,YAAL,GAAoBD,SAAS,CAACjG,eAAV,CAA0B,CAA1B,EAA6B+E,UAAjD;IACAiB,YAAY,CAACxM,IAAb,CAAkB,IAAlB,EAAwByM,SAAxB;GAFD,MAGO,IAAI/H,SAAS,IAAIzF,SAAS,GAAGC,YAAhB,CAAb,EAA4C;IAClDsN,YAAY,CAACxM,IAAb,CAAkB,IAAlB,EAAwByM,SAAxB;;;;AAGF,SAASQ,gBAAT,CAA0BR,SAA1B,EAAqC;MAC9BtJ,CAAC,GAAGsJ,SAAS,CAAC1K,QAAV,CAAmBqB,OAA7B;MACMC,CAAC,GAAGoJ,SAAS,CAAC1K,QAAV,CAAmBuB,OAA7B;;OAEK,IAAItG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK6P,WAAL,CAAiBpR,MAArC,EAA6CuB,CAAC,EAA9C,EAAkD;QAC3CkQ,CAAC,GAAG,KAAKL,WAAL,CAAiB7P,CAAjB,CAAV;QACMmQ,EAAE,GAAG/Q,IAAI,CAACC,GAAL,CAAS8G,CAAC,GAAG+J,CAAC,CAAC/J,CAAf,CAAX;QACMiK,EAAE,GAAGhR,IAAI,CAACC,GAAL,CAASgH,CAAC,GAAG6J,CAAC,CAAC7J,CAAf,CAAX;;QAEI8J,EAAE,IAAIZ,cAAN,IAAwBa,EAAE,IAAIb,cAAlC,EAAkD;aAC1C,IAAP;;;;SAGK,KAAP;;;IAIoBc;;;MAAAA;;;;;6BACRxM,QAAZ,EAAqB+G,QAArB,EAA+B;;;gCACxB/G,QAAN,EAAe+G,QAAf;;YAeDX,OAhB+B,GAgBrB,UAACpG,OAAD,EAAUyM,UAAV,EAAsBC,SAAtB,EAAoC;YACvC1D,OAAO,GAAI0D,SAAS,CAAC3D,WAAV,KAA0BlL,gBAA3C;YACM8O,OAAO,GAAID,SAAS,CAAC3D,WAAV,KAA0BhL,gBAA3C;;YAEI4O,OAAO,IAAID,SAAS,CAACE,kBAArB,IAA2CF,SAAS,CAACE,kBAAV,CAA6BC,gBAA5E,EAA8F;;SAJjD;;;YASzC7D,OAAJ,EAAa;UACZmD,aAAa,CAAChN,IAAd,wDAAyBsN,UAAzB,EAAqCC,SAArC;SADD,MAEO,IAAIC,OAAO,IAAIP,gBAAgB,CAACjN,IAAjB,wDAA4BuN,SAA5B,CAAf,EAAuD;;;;cAIzD3F,QAAL,CAAc/G,OAAd,EAAuByM,UAAvB,EAAmCC,SAAnC;OA/B8B;;YAGzB3B,KAAL,GAAa,IAAIV,UAAJ,CAAe,MAAKrK,OAApB,EAA6B,MAAKoG,OAAlC,CAAb;YACK0G,KAAL,GAAa,IAAIxB,UAAJ,CAAe,MAAKtL,OAApB,EAA6B,MAAKoG,OAAlC,CAAb;YACKyF,YAAL,GAAoB,IAApB;YACKG,WAAL,GAAmB,EAAnB;;;;;;;;;;;;;;;;;;WAgCDxE,6BAAU;WACJuD,KAAL,CAAWvD,OAAX;WACKsF,KAAL,CAAWtF,OAAX;;;;IAzC2CV;;SAAxB0F;;;AClErB;;;;;;;;AAOA,AAAe,SAASO,mBAAT,CAA6B/M,OAA7B,EAAsC;MAC/CgN,IAAJ,CADmD;;MAGnCC,UAHmC,GAGlBjN,OAHkB,CAG7CQ,OAH6C,CAGnCyM,UAHmC;;MAI/CA,UAAJ,EAAgB;IACdD,IAAI,GAAGC,UAAP;GADF,MAEO,IAAIzP,sBAAJ,EAA4B;IACjCwP,IAAI,GAAGzE,iBAAP;GADK,MAEA,IAAI9K,kBAAJ,EAAwB;IAC7BuP,IAAI,GAAG3C,UAAP;GADK,MAEA,IAAI,CAAC9M,aAAL,EAAoB;IACzByP,IAAI,GAAG1B,UAAP;GADK,MAEA;IACL0B,IAAI,GAAGR,eAAP;;;SAEK,IAAKQ,IAAL,CAAWhN,OAAX,EAAoBwF,YAApB,CAAP;;;AC5BF;;;;;;;;;;;AAUA,AAAe,SAAS0H,cAAT,CAAwBC,GAAxB,EAA6BC,EAA7B,EAAiClO,OAAjC,EAA0C;MACnDqK,KAAK,CAAC8D,OAAN,CAAcF,GAAd,CAAJ,EAAwB;IACtBnO,IAAI,CAACmO,GAAD,EAAMjO,OAAO,CAACkO,EAAD,CAAb,EAAmBlO,OAAnB,CAAJ;WACO,IAAP;;;SAEK,KAAP;;;IChBIoO,cAAc,GAAG,CAAvB;AACA,IAAMC,WAAW,GAAG,CAApB;AACA,IAAMC,aAAa,GAAG,CAAtB;AACA,IAAMC,WAAW,GAAG,CAApB;AACA,IAAMC,gBAAgB,GAAGD,WAAzB;AACA,IAAME,eAAe,GAAG,EAAxB;AACA,IAAMC,YAAY,GAAG,EAArB;;ACNA;;;;;AAKA,IAAIC,SAAS,GAAG,CAAhB;AACA,AAAe,SAASC,QAAT,GAAoB;SAC1BD,SAAS,EAAhB;;;ACPF;;;;;;;AAOA,AAAe,SAASE,4BAAT,CAAsCC,eAAtC,EAAuDrN,UAAvD,EAAmE;MAC1EX,OAD0E,GAC9DW,UAD8D,CAC1EX,OAD0E;;MAE5EA,OAAJ,EAAa;WACJA,OAAO,CAACiO,GAAR,CAAYD,eAAZ,CAAP;;;SAEKA,eAAP;;;ACLF;;;;;;;AAMA,AAAe,SAASE,QAAT,CAAkBC,KAAlB,EAAyB;MAClCA,KAAK,GAAGR,eAAZ,EAA6B;WACpB,QAAP;GADF,MAEO,IAAIQ,KAAK,GAAGV,WAAZ,EAAyB;WACvB,KAAP;GADK,MAEA,IAAIU,KAAK,GAAGX,aAAZ,EAA2B;WACzB,MAAP;GADK,MAEA,IAAIW,KAAK,GAAGZ,WAAZ,EAAyB;WACvB,OAAP;;;SAEK,EAAP;;;ACNF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoCqBa;;;sBACP5N,OAAZ,EAA0B;QAAdA,OAAc;MAAdA,OAAc,GAAJ,EAAI;;;SACnBA,OAAL;MACEI,MAAM,EAAE;OACLJ,OAFL;SAKK6N,EAAL,GAAUP,QAAQ,EAAlB;SAEK9N,OAAL,GAAe,IAAf,CARwB;;SAWnBmO,KAAL,GAAab,cAAb;SACKgB,YAAL,GAAoB,EAApB;SACKC,WAAL,GAAmB,EAAnB;;;;;;;;;;;;SASFrO,mBAAIM,SAAS;IACXpG,QAAM,CAAC,KAAKoG,OAAN,EAAeA,OAAf,CAAN,CADW;;SAINR,OAAL,IAAgB,KAAKA,OAAL,CAAaS,WAAb,CAAyBF,MAAzB,EAAhB;WACO,IAAP;;;;;;;;;;SASFiO,uCAAcR,iBAAiB;QACzBd,cAAc,CAACc,eAAD,EAAkB,eAAlB,EAAmC,IAAnC,CAAlB,EAA4D;aACnD,IAAP;;;QAGIM,YALuB,GAKN,IALM,CAKvBA,YALuB;IAM7BN,eAAe,GAAGD,4BAA4B,CAACC,eAAD,EAAkB,IAAlB,CAA9C;;QACI,CAACM,YAAY,CAACN,eAAe,CAACK,EAAjB,CAAjB,EAAuC;MACrCC,YAAY,CAACN,eAAe,CAACK,EAAjB,CAAZ,GAAmCL,eAAnC;MACAA,eAAe,CAACQ,aAAhB,CAA8B,IAA9B;;;WAEK,IAAP;;;;;;;;;;SASFC,+CAAkBT,iBAAiB;QAC7Bd,cAAc,CAACc,eAAD,EAAkB,mBAAlB,EAAuC,IAAvC,CAAlB,EAAgE;aACvD,IAAP;;;IAGFA,eAAe,GAAGD,4BAA4B,CAACC,eAAD,EAAkB,IAAlB,CAA9C;WACO,KAAKM,YAAL,CAAkBN,eAAe,CAACK,EAAlC,CAAP;WACO,IAAP;;;;;;;;;;SASFK,yCAAeV,iBAAiB;QAC1Bd,cAAc,CAACc,eAAD,EAAkB,gBAAlB,EAAoC,IAApC,CAAlB,EAA6D;aACpD,IAAP;;;QAGIO,WALwB,GAKR,IALQ,CAKxBA,WALwB;IAM9BP,eAAe,GAAGD,4BAA4B,CAACC,eAAD,EAAkB,IAAlB,CAA9C;;QACIvG,OAAO,CAAC8G,WAAD,EAAcP,eAAd,CAAP,KAA0C,CAAC,CAA/C,EAAkD;MAChDO,WAAW,CAACnF,IAAZ,CAAiB4E,eAAjB;MACAA,eAAe,CAACU,cAAhB,CAA+B,IAA/B;;;WAEK,IAAP;;;;;;;;;;SASFC,iDAAmBX,iBAAiB;QAC9Bd,cAAc,CAACc,eAAD,EAAkB,oBAAlB,EAAwC,IAAxC,CAAlB,EAAiE;aACxD,IAAP;;;IAGFA,eAAe,GAAGD,4BAA4B,CAACC,eAAD,EAAkB,IAAlB,CAA9C;QACItT,KAAK,GAAG+M,OAAO,CAAC,KAAK8G,WAAN,EAAmBP,eAAnB,CAAnB;;QACItT,KAAK,GAAG,CAAC,CAAb,EAAgB;WACT6T,WAAL,CAAiBlF,MAAjB,CAAwB3O,KAAxB,EAA+B,CAA/B;;;WAEK,IAAP;;;;;;;;;SAQFkU,mDAAqB;WACZ,KAAKL,WAAL,CAAiB3T,MAAjB,GAA0B,CAAjC;;;;;;;;;;SASFiU,6CAAiBb,iBAAiB;WACzB,CAAC,CAAC,KAAKM,YAAL,CAAkBN,eAAe,CAACK,EAAlC,CAAT;;;;;;;;;;SASFvI,qBAAK7E,OAAO;QACN+F,IAAI,GAAG,IAAX;QACMmH,KAFI,GAEM,IAFN,CAEJA,KAFI;;aAIDrI,IAAT,CAAcgJ,KAAd,EAAqB;MACnB9H,IAAI,CAAChH,OAAL,CAAa8F,IAAb,CAAkBgJ,KAAlB,EAAyB7N,KAAzB;KALQ;;;QASNkN,KAAK,GAAGV,WAAZ,EAAyB;MACvB3H,IAAI,CAACkB,IAAI,CAACxG,OAAL,CAAasO,KAAb,GAAqBZ,QAAQ,CAACC,KAAD,CAA9B,CAAJ;;;IAGFrI,IAAI,CAACkB,IAAI,CAACxG,OAAL,CAAasO,KAAd,CAAJ,CAbU;;QAeN7N,KAAK,CAAC8N,eAAV,EAA2B;;MACzBjJ,IAAI,CAAC7E,KAAK,CAAC8N,eAAP,CAAJ;KAhBQ;;;QAoBNZ,KAAK,IAAIV,WAAb,EAA0B;MACxB3H,IAAI,CAACkB,IAAI,CAACxG,OAAL,CAAasO,KAAb,GAAqBZ,QAAQ,CAACC,KAAD,CAA9B,CAAJ;;;;;;;;;;;;SAWJa,2BAAQ/N,OAAO;QACT,KAAKgO,OAAL,EAAJ,EAAoB;aACX,KAAKnJ,IAAL,CAAU7E,KAAV,CAAP;KAFW;;;SAKRkN,KAAL,GAAaP,YAAb;;;;;;;;;SAQFqB,6BAAU;QACJ9S,CAAC,GAAG,CAAR;;WACOA,CAAC,GAAG,KAAKoS,WAAL,CAAiB3T,MAA5B,EAAoC;UAC9B,EAAE,KAAK2T,WAAL,CAAiBpS,CAAjB,EAAoBgS,KAApB,IAA6BP,YAAY,GAAGN,cAA5C,CAAF,CAAJ,EAAoE;eAC3D,KAAP;;;MAEFnR,CAAC;;;WAEI,IAAP;;;;;;;;;SAQF4J,+BAAU2G,WAAW;;;QAGfwC,cAAc,GAAG9U,QAAM,CAAC,EAAD,EAAKsS,SAAL,CAA3B,CAHmB;;QAMf,CAACtN,QAAQ,CAAC,KAAKoB,OAAL,CAAaI,MAAd,EAAsB,CAAC,IAAD,EAAOsO,cAAP,CAAtB,CAAb,EAA4D;WACrDC,KAAL;WACKhB,KAAL,GAAaP,YAAb;;KARiB;;;QAaf,KAAKO,KAAL,IAAcT,gBAAgB,GAAGC,eAAnB,GAAqCC,YAAnD,CAAJ,EAAsE;WAC/DO,KAAL,GAAab,cAAb;;;SAGGa,KAAL,GAAa,KAAKiB,OAAL,CAAaF,cAAb,CAAb,CAjBmB;;;QAqBf,KAAKf,KAAL,IAAcZ,WAAW,GAAGC,aAAd,GAA8BC,WAA9B,GAA4CE,eAA1D,CAAJ,EAAgF;WACzEqB,OAAL,CAAaE,cAAb;;;;;;;;;;;;;;;SAcJE,2BAAQ1C,WAAW;;;;;;;;;;;SASnB5L,2CAAiB;;;;;;;;;SAQjBqO,yBAAQ;;;;;AChSV;;;;;;;;;;;;IAWqBE;;;;;yBACP7O,OAAZ,EAA0B;;;QAAdA,OAAc;MAAdA,OAAc,GAAJ,EAAI;;;;MAEtBsO,KAAK,EAAE,KADT;MAEEpN,QAAQ,EAAE,CAFZ;MAGE4N,IAAI,EAAE,CAHR;MAIEC,QAAQ,EAAE,GAJZ;;MAKEC,IAAI,EAAE,GALR;;MAMEC,SAAS,EAAE,CANb;;MAOEC,YAAY,EAAE;OACXlP,OARL,WADwB;;;UAcnBmP,KAAL,GAAa,KAAb;UACKC,OAAL,GAAe,KAAf;UAEKC,MAAL,GAAc,IAAd;UACKC,MAAL,GAAc,IAAd;UACKC,KAAL,GAAa,CAAb;;;;;;SAGFjP,2CAAiB;WACR,CAAC7D,yBAAD,CAAP;;;SAGFmS,2BAAQnO,OAAO;;;QACPT,OADO,GACK,IADL,CACPA,OADO;QAGTwP,aAAa,GAAG/O,KAAK,CAACS,QAAN,CAAe9G,MAAf,KAA0B4F,OAAO,CAACkB,QAAtD;QACIuO,aAAa,GAAGhP,KAAK,CAACW,QAAN,GAAiBpB,OAAO,CAACiP,SAA7C;QACIS,cAAc,GAAGjP,KAAK,CAACa,SAAN,GAAkBtB,OAAO,CAACgP,IAA/C;SAEKL,KAAL;;QAEKlO,KAAK,CAAC4C,SAAN,GAAkB3F,WAAnB,IAAoC,KAAK6R,KAAL,KAAe,CAAvD,EAA2D;aAClD,KAAKI,WAAL,EAAP;KAVW;;;;QAeTF,aAAa,IAAIC,cAAjB,IAAmCF,aAAvC,EAAsD;UAChD/O,KAAK,CAAC4C,SAAN,KAAoBzF,SAAxB,EAAmC;eAC1B,KAAK+R,WAAL,EAAP;;;UAGEC,aAAa,GAAG,KAAKT,KAAL,GAAc1O,KAAK,CAAC0B,SAAN,GAAkB,KAAKgN,KAAvB,GAA+BnP,OAAO,CAAC+O,QAArD,GAAiE,IAArF;UACIc,aAAa,GAAG,CAAC,KAAKT,OAAN,IAAiB7M,WAAW,CAAC,KAAK6M,OAAN,EAAe3O,KAAK,CAAC2B,MAArB,CAAX,GAA0CpC,OAAO,CAACkP,YAAvF;WAEKC,KAAL,GAAa1O,KAAK,CAAC0B,SAAnB;WACKiN,OAAL,GAAe3O,KAAK,CAAC2B,MAArB;;UAEI,CAACyN,aAAD,IAAkB,CAACD,aAAvB,EAAsC;aAC/BL,KAAL,GAAa,CAAb;OADF,MAEO;aACAA,KAAL,IAAc,CAAd;;;WAGGD,MAAL,GAAc7O,KAAd,CAjBoD;;;UAqBhDqP,QAAQ,GAAG,KAAKP,KAAL,GAAavP,OAAO,CAAC8O,IAApC;;UACIgB,QAAQ,KAAK,CAAjB,EAAoB;;;YAGd,CAAC,KAAK1B,kBAAL,EAAL,EAAgC;iBACvBlB,gBAAP;SADF,MAEO;eACAmC,MAAL,GAAc3D,UAAU,CAAC,YAAM;YAC7B,MAAI,CAACiC,KAAL,GAAaT,gBAAb;;YACA,MAAI,CAACsB,OAAL;WAFsB,EAGrBxO,OAAO,CAAC+O,QAHa,CAAxB;iBAIOhC,WAAP;;;;;WAICK,YAAP;;;SAGFuC,qCAAc;;;SACPN,MAAL,GAAc3D,UAAU,CAAC,YAAM;MAC7B,MAAI,CAACiC,KAAL,GAAaP,YAAb;KADsB,EAErB,KAAKpN,OAAL,CAAa+O,QAFQ,CAAxB;WAGO3B,YAAP;;;SAGFuB,yBAAQ;IACNoB,YAAY,CAAC,KAAKV,MAAN,CAAZ;;;SAGF/J,uBAAO;QACD,KAAKqI,KAAL,KAAeT,gBAAnB,EAAqC;WAC9BoC,MAAL,CAAYQ,QAAZ,GAAuB,KAAKP,KAA5B;WACK/P,OAAL,CAAa8F,IAAb,CAAkB,KAAKtF,OAAL,CAAasO,KAA/B,EAAsC,KAAKgB,MAA3C;;;;;EA/FqC1B;;ACR3C;;;;;;;IAMqBoC;;;;;0BACPhQ,OAAZ,EAA0B;QAAdA,OAAc;MAAdA,OAAc,GAAJ,EAAI;;;WACxB;MACEkB,QAAQ,EAAE;OACPlB,OAFL,EADwB;;;;;;;;;;;;;SAc1BiQ,6BAASxP,OAAO;QACVyP,cAAc,GAAG,KAAKlQ,OAAL,CAAakB,QAAlC;WACOgP,cAAc,KAAK,CAAnB,IAAwBzP,KAAK,CAACS,QAAN,CAAe9G,MAAf,KAA0B8V,cAAzD;;;;;;;;;;;SAUFtB,2BAAQnO,OAAO;QACPkN,KADO,GACG,IADH,CACPA,KADO;QAEPtK,SAFO,GAEO5C,KAFP,CAEP4C,SAFO;QAIT8M,YAAY,GAAGxC,KAAK,IAAIZ,WAAW,GAAGC,aAAlB,CAAxB;QACIoD,OAAO,GAAG,KAAKH,QAAL,CAAcxP,KAAd,CAAd,CALa;;QAQT0P,YAAY,KAAK9M,SAAS,GAAGxF,YAAZ,IAA4B,CAACuS,OAAlC,CAAhB,EAA4D;aACnDzC,KAAK,GAAGR,eAAf;KADF,MAEO,IAAIgD,YAAY,IAAIC,OAApB,EAA6B;UAC9B/M,SAAS,GAAGzF,SAAhB,EAA2B;eAClB+P,KAAK,GAAGV,WAAf;OADF,MAEO,IAAI,EAAEU,KAAK,GAAGZ,WAAV,CAAJ,EAA4B;eAC1BA,WAAP;;;aAEKY,KAAK,GAAGX,aAAf;;;WAEKI,YAAP;;;;EA7CwCQ;;ACZ5C;;;;;;;AAMA,AAAe,SAASyC,YAAT,CAAsB1P,SAAtB,EAAiC;MAC1CA,SAAS,KAAKzC,cAAlB,EAAkC;WACzB,MAAP;GADF,MAEO,IAAIyC,SAAS,KAAK1C,YAAlB,EAAgC;WAC9B,IAAP;GADK,MAEA,IAAI0C,SAAS,KAAK5C,cAAlB,EAAkC;WAChC,MAAP;GADK,MAEA,IAAI4C,SAAS,KAAK3C,eAAlB,EAAmC;WACjC,OAAP;;;SAEK,EAAP;;;ACRF;;;;;;;;IAOqBsS;;;;;yBACPtQ,OAAZ,EAA0B;;;QAAdA,OAAc;MAAdA,OAAc,GAAJ,EAAI;;;;MAEtBsO,KAAK,EAAE,KADT;MAEEW,SAAS,EAAE,EAFb;MAGE/N,QAAQ,EAAE,CAHZ;MAIEP,SAAS,EAAEtC;OACR2B,OALL;UAOKuQ,EAAL,GAAU,IAAV;UACKC,EAAL,GAAU,IAAV;;;;;;SAGFlQ,2CAAiB;QACCK,SADD,GACiB,IADjB,CACTX,OADS,CACCW,SADD;QAEXvB,OAAO,GAAG,EAAd;;QACIuB,SAAS,GAAGxC,oBAAhB,EAAsC;MACpCiB,OAAO,CAACwJ,IAAR,CAAahM,kBAAb;;;QAEE+D,SAAS,GAAGvC,kBAAhB,EAAoC;MAClCgB,OAAO,CAACwJ,IAAR,CAAajM,kBAAb;;;WAEKyC,OAAP;;;SAGFqR,uCAAchQ,OAAO;QACbT,OADa,GACD,IADC,CACbA,OADa;QAEf0Q,QAAQ,GAAG,IAAf;QACMtP,QAHa,GAGAX,KAHA,CAGbW,QAHa;QAIbT,SAJa,GAICF,KAJD,CAIbE,SAJa;QAKfmB,CAAC,GAAGrB,KAAK,CAAC4B,MAAd;QACIL,CAAC,GAAGvB,KAAK,CAAC6B,MAAd,CANmB;;QASf,EAAE3B,SAAS,GAAGX,OAAO,CAACW,SAAtB,CAAJ,EAAsC;UAChCX,OAAO,CAACW,SAAR,GAAoBxC,oBAAxB,EAA8C;QAC5CwC,SAAS,GAAImB,CAAC,KAAK,CAAP,GAAYhE,cAAZ,GAA8BgE,CAAC,GAAG,CAAL,GAAU/D,cAAV,GAA2BC,eAApE;QACA0S,QAAQ,GAAG5O,CAAC,KAAK,KAAKyO,EAAtB;QACAnP,QAAQ,GAAGrG,IAAI,CAACC,GAAL,CAASyF,KAAK,CAAC4B,MAAf,CAAX;OAHF,MAIO;QACL1B,SAAS,GAAIqB,CAAC,KAAK,CAAP,GAAYlE,cAAZ,GAA8BkE,CAAC,GAAG,CAAL,GAAU/D,YAAV,GAAyBC,cAAlE;QACAwS,QAAQ,GAAG1O,CAAC,KAAK,KAAKwO,EAAtB;QACApP,QAAQ,GAAGrG,IAAI,CAACC,GAAL,CAASyF,KAAK,CAAC6B,MAAf,CAAX;;;;IAGJ7B,KAAK,CAACE,SAAN,GAAkBA,SAAlB;WACO+P,QAAQ,IAAItP,QAAQ,GAAGpB,OAAO,CAACiP,SAA/B,IAA4CtO,SAAS,GAAGX,OAAO,CAACW,SAAvE;;;SAGFsP,6BAASxP,OAAO;WACPuP,cAAc,CAAC/H,SAAf,CAAyBgI,QAAzB,CAAkCtR,IAAlC,CAAuC,IAAvC,EAA6C8B,KAA7C;SACGkN,KAAL,GAAaZ,WAAb,IAA6B,EAAE,KAAKY,KAAL,GAAaZ,WAAf,KAA+B,KAAK0D,aAAL,CAAmBhQ,KAAnB,CAD1D,CAAP;;;SAIF6E,qBAAK7E,OAAO;SAEL8P,EAAL,GAAU9P,KAAK,CAAC4B,MAAhB;SACKmO,EAAL,GAAU/P,KAAK,CAAC6B,MAAhB;QAEI3B,SAAS,GAAG0P,YAAY,CAAC5P,KAAK,CAACE,SAAP,CAA5B;;QAEIA,SAAJ,EAAe;MACbF,KAAK,CAAC8N,eAAN,GAAwB,KAAKvO,OAAL,CAAasO,KAAb,GAAqB3N,SAA7C;;;8BAEI2E,IAAN,YAAW7E,KAAX;;;;EAhEuCuP;;ACf3C;;;;;;;;IAOqBW;;;;;2BACP3Q,OAAZ,EAA0B;QAAdA,OAAc;MAAdA,OAAc,GAAJ,EAAI;;;WACxB;MACEsO,KAAK,EAAE,OADT;MAEEW,SAAS,EAAE,EAFb;MAGEnL,QAAQ,EAAE,GAHZ;MAIEnD,SAAS,EAAExC,oBAAoB,GAAGC,kBAJpC;MAKE8C,QAAQ,EAAE;OACPlB,OANL,EADwB;;;;;SAW1BM,2CAAiB;WACRgQ,aAAa,CAACrI,SAAd,CAAwB3H,cAAxB,CAAuC3B,IAAvC,CAA4C,IAA5C,CAAP;;;SAGFsR,6BAASxP,OAAO;QACRE,SADQ,GACM,KAAKX,OADX,CACRW,SADQ;QAEVmD,QAAJ;;QAEInD,SAAS,IAAIxC,oBAAoB,GAAGC,kBAA3B,CAAb,EAA6D;MAC3D0F,QAAQ,GAAGrD,KAAK,CAAC8D,eAAjB;KADF,MAEO,IAAI5D,SAAS,GAAGxC,oBAAhB,EAAsC;MAC3C2F,QAAQ,GAAGrD,KAAK,CAAC+D,gBAAjB;KADK,MAEA,IAAI7D,SAAS,GAAGvC,kBAAhB,EAAoC;MACzC0F,QAAQ,GAAGrD,KAAK,CAACgE,gBAAjB;;;WAGK,0BAAMwL,QAAN,YAAexP,KAAf,KACHE,SAAS,GAAGF,KAAK,CAACG,eADf,IAEHH,KAAK,CAACW,QAAN,GAAiB,KAAKpB,OAAL,CAAaiP,SAF3B,IAGHxO,KAAK,CAACmE,WAAN,KAAsB,KAAK5E,OAAL,CAAakB,QAHhC,IAIHlG,GAAG,CAAC8I,QAAD,CAAH,GAAgB,KAAK9D,OAAL,CAAa8D,QAJ1B,IAIsCrD,KAAK,CAAC4C,SAAN,GAAkBzF,SAJ/D;;;SAOF0H,qBAAK7E,OAAO;QACNE,SAAS,GAAG0P,YAAY,CAAC5P,KAAK,CAACG,eAAP,CAA5B;;QACID,SAAJ,EAAe;WACRnB,OAAL,CAAa8F,IAAb,CAAkB,KAAKtF,OAAL,CAAasO,KAAb,GAAqB3N,SAAvC,EAAkDF,KAAlD;;;SAGGjB,OAAL,CAAa8F,IAAb,CAAkB,KAAKtF,OAAL,CAAasO,KAA/B,EAAsC7N,KAAtC;;;;EAzCyCuP;;ACV7C;;;;;;;;IAOqBY;;;;;2BACP5Q,OAAZ,EAA0B;QAAdA,OAAc;MAAdA,OAAc,GAAJ,EAAI;;;WACxB;MACEsO,KAAK,EAAE,OADT;MAEEW,SAAS,EAAE,CAFb;MAGE/N,QAAQ,EAAE;OACPlB,OAJL,EADwB;;;;;SAS1BM,2CAAiB;WACR,CAAC5D,iBAAD,CAAP;;;SAGFuT,6BAASxP,OAAO;WACP,0BAAMwP,QAAN,YAAexP,KAAf,MACF1F,IAAI,CAACC,GAAL,CAASyF,KAAK,CAACiE,KAAN,GAAc,CAAvB,IAA4B,KAAK1E,OAAL,CAAaiP,SAAzC,IAAsD,KAAKtB,KAAL,GAAaZ,WADjE,CAAP;;;SAIFzH,qBAAK7E,OAAO;QACNA,KAAK,CAACiE,KAAN,KAAgB,CAApB,EAAuB;UACjBmM,KAAK,GAAGpQ,KAAK,CAACiE,KAAN,GAAc,CAAd,GAAkB,IAAlB,GAAyB,KAArC;MACAjE,KAAK,CAAC8N,eAAN,GAAwB,KAAKvO,OAAL,CAAasO,KAAb,GAAqBuC,KAA7C;;;8BAEIvL,IAAN,YAAW7E,KAAX;;;;EAxByCuP;;ACP7C;;;;;;;;IAOqBc;;;;;4BACP9Q,OAAZ,EAA0B;QAAdA,OAAc;MAAdA,OAAc,GAAJ,EAAI;;;WACxB;MACEsO,KAAK,EAAE,QADT;MAEEW,SAAS,EAAE,CAFb;MAGE/N,QAAQ,EAAE;OACPlB,OAJL,EADwB;;;;;SAS1BM,2CAAiB;WACR,CAAC5D,iBAAD,CAAP;;;SAGFuT,6BAASxP,OAAO;WACP,0BAAMwP,QAAN,YAAexP,KAAf,MACF1F,IAAI,CAACC,GAAL,CAASyF,KAAK,CAACkE,QAAf,IAA2B,KAAK3E,OAAL,CAAaiP,SAAxC,IAAqD,KAAKtB,KAAL,GAAaZ,WADhE,CAAP;;;;EAf0CiD;;ACE9C;;;;;;;;IAOqBe;;;;;2BACP/Q,OAAZ,EAA0B;;;QAAdA,OAAc;MAAdA,OAAc,GAAJ,EAAI;;;;MAEtBsO,KAAK,EAAE,OADT;MAEEpN,QAAQ,EAAE,CAFZ;MAGE8N,IAAI,EAAE,GAHR;;MAIEC,SAAS,EAAE;OACRjP,OALL;UAOKqP,MAAL,GAAc,IAAd;UACKC,MAAL,GAAc,IAAd;;;;;;SAGFhP,2CAAiB;WACR,CAAC9D,iBAAD,CAAP;;;SAGFoS,2BAAQnO,OAAO;;;QACPT,OADO,GACK,IADL,CACPA,OADO;QAETwP,aAAa,GAAG/O,KAAK,CAACS,QAAN,CAAe9G,MAAf,KAA0B4F,OAAO,CAACkB,QAAtD;QACIuO,aAAa,GAAGhP,KAAK,CAACW,QAAN,GAAiBpB,OAAO,CAACiP,SAA7C;QACI+B,SAAS,GAAGvQ,KAAK,CAACa,SAAN,GAAkBtB,OAAO,CAACgP,IAA1C;SAEKM,MAAL,GAAc7O,KAAd,CANa;;;QAUT,CAACgP,aAAD,IAAkB,CAACD,aAAnB,IAAqC/O,KAAK,CAAC4C,SAAN,IAAmBzF,SAAS,GAAGC,YAA/B,KAAgD,CAACmT,SAA1F,EAAsG;WAC/FrC,KAAL;KADF,MAEO,IAAIlO,KAAK,CAAC4C,SAAN,GAAkB3F,WAAtB,EAAmC;WACnCiR,KAAL;WACKU,MAAL,GAAc3D,UAAU,CAAC,YAAM;QAC7B,MAAI,CAACiC,KAAL,GAAaT,gBAAb;;QACA,MAAI,CAACsB,OAAL;OAFsB,EAGrBxO,OAAO,CAACgP,IAHa,CAAxB;KAFK,MAMA,IAAIvO,KAAK,CAAC4C,SAAN,GAAkBzF,SAAtB,EAAiC;aAC/BsP,gBAAP;;;WAEKE,YAAP;;;SAGFuB,yBAAQ;IACNoB,YAAY,CAAC,KAAKV,MAAN,CAAZ;;;SAGF/J,qBAAK7E,OAAO;QACN,KAAKkN,KAAL,KAAeT,gBAAnB,EAAqC;;;;QAIjCzM,KAAK,IAAKA,KAAK,CAAC4C,SAAN,GAAkBzF,SAAhC,EAA4C;WACrC4B,OAAL,CAAa8F,IAAb,CAAqB,KAAKtF,OAAL,CAAasO,KAAlC,SAA6C7N,KAA7C;KADF,MAEO;WACA6O,MAAL,CAAYnN,SAAZ,GAAwBlH,GAAG,EAA3B;WACKuE,OAAL,CAAa8F,IAAb,CAAkB,KAAKtF,OAAL,CAAasO,KAA/B,EAAsC,KAAKgB,MAA3C;;;;;EAtDuC1B;;ACX7C,eAAe;;;;;;;;EAQdqD,SAAS,EAAE,KARG;;;;;;;;;EAiBdhR,WAAW,EAAE1D,oBAjBC;;;;;;;EAwBd6D,MAAM,EAAE,IAxBM;;;;;;;;;;EAkCdqG,WAAW,EAAE,IAlCC;;;;;;;;EA0CdgG,UAAU,EAAE,IA1CE;;;;;;;;EAkDdyE,QAAQ,EAAE;;;;;;;IAOTC,UAAU,EAAE,MAPH;;;;;;;;IAeTC,WAAW,EAAE,MAfJ;;;;;;;;;;IAyBTC,YAAY,EAAE,MAzBL;;;;;;;;IAiCTC,cAAc,EAAE,MAjCP;;;;;;;;IAyCTC,QAAQ,EAAE,MAzCD;;;;;;;;;IAkDTC,iBAAiB,EAAE;;CApGrB;;;;;;;;;AA+GA,AAAO,IAAMC,MAAM,GAAG,CACpB,CAACX,gBAAD,EAAmB;EAAE1Q,MAAM,EAAE;CAA7B,CADoB,EAEpB,CAACwQ,eAAD,EAAkB;EAAExQ,MAAM,EAAE;CAA5B,EAAqC,CAAC,QAAD,CAArC,CAFoB,EAGpB,CAACuQ,eAAD,EAAkB;EAAEhQ,SAAS,EAAExC;CAA/B,CAHoB,EAIpB,CAACmS,aAAD,EAAgB;EAAE3P,SAAS,EAAExC;CAA7B,EAAqD,CAAC,OAAD,CAArD,CAJoB,EAKpB,CAAC0Q,aAAD,CALoB,EAMpB,CAACA,aAAD,EAAgB;EAAEP,KAAK,EAAE,WAAT;EAAsBQ,IAAI,EAAE;CAA5C,EAAiD,CAAC,KAAD,CAAjD,CANoB,EAOpB,CAACiC,eAAD,CAPoB,CAAf;;ACvGP,IAAMW,IAAI,GAAG,CAAb;AACA,IAAMC,WAAW,GAAG,CAApB;;;;;;;;AASA,SAASC,cAAT,CAAwBpS,OAAxB,EAAiCqS,GAAjC,EAAsC;MAC5BjS,OAD4B,GAChBJ,OADgB,CAC5BI,OAD4B;;MAGhC,CAACA,OAAO,CAACjF,KAAb,EAAoB;;;;MAGhBY,IAAJ;EAEAiD,IAAI,CAACgB,OAAO,CAACQ,OAAR,CAAgBkR,QAAjB,EAA2B,UAACzR,KAAD,EAAQqS,IAAR,EAAiB;IAC9CvW,IAAI,GAAGJ,QAAQ,CAACyE,OAAO,CAACjF,KAAT,EAAgBmX,IAAhB,CAAf;;QACID,GAAJ,EAAS;MACPrS,OAAO,CAACuS,WAAR,CAAoBxW,IAApB,IAA4BqE,OAAO,CAACjF,KAAR,CAAcY,IAAd,CAA5B;MACAqE,OAAO,CAACjF,KAAR,CAAcY,IAAd,IAAsBkE,KAAtB;KAFF,MAGO;MACLG,OAAO,CAACjF,KAAR,CAAcY,IAAd,IAAsBiE,OAAO,CAACuS,WAAR,CAAoBxW,IAApB,KAA6B,EAAnD;;GANA,CAAJ;;MASI,CAACsW,GAAL,EAAU;IACRrS,OAAO,CAACuS,WAAR,GAAsB,EAAtB;;;;;;;;;;;AAUJ,SAASC,eAAT,CAAyB1D,KAAzB,EAAgC2D,IAAhC,EAAsC;MAC9BC,YAAY,GAAGxX,QAAQ,CAACyX,WAAT,CAAqB,OAArB,CAArB;EAEAD,YAAY,CAACE,SAAb,CAAuB9D,KAAvB,EAA8B,IAA9B,EAAoC,IAApC;EACA4D,YAAY,CAACG,OAAb,GAAuBJ,IAAvB;EACAA,IAAI,CAACnY,MAAL,CAAYwY,aAAZ,CAA0BJ,YAA1B;;;;;;;;;;;IAWmBK;;;mBACP3S,OAAZ,EAAqBI,OAArB,EAA8B;;;SACvBA,OAAL,GAAepG,QAAM,CAAC,EAAD,EAAK4Y,QAAL,EAAexS,OAAO,IAAI,EAA1B,CAArB;SAEKA,OAAL,CAAayG,WAAb,GAA2B,KAAKzG,OAAL,CAAayG,WAAb,IAA4B7G,OAAvD;SAEK6S,QAAL,GAAgB,EAAhB;SACK5R,OAAL,GAAe,EAAf;SACKX,WAAL,GAAmB,EAAnB;SACK6R,WAAL,GAAmB,EAAnB;SAEKnS,OAAL,GAAeA,OAAf;SACKa,KAAL,GAAa8L,mBAAmB,CAAC,IAAD,CAAhC;SACKtM,WAAL,GAAmB,IAAIV,WAAJ,CAAgB,IAAhB,EAAsB,KAAKS,OAAL,CAAaC,WAAnC,CAAnB;IAEA2R,cAAc,CAAC,IAAD,EAAO,IAAP,CAAd;IAEApT,IAAI,CAAC,KAAKwB,OAAL,CAAaE,WAAd,EAA2B,UAAAwS,IAAI,EAAI;UAC/BvS,UAAU,GAAG,KAAI,CAAC0R,GAAL,CAAS,IAAKa,IAAI,CAAC,CAAD,CAAT,CAAcA,IAAI,CAAC,CAAD,CAAlB,CAAT,CAAnB;;MAEAA,IAAI,CAAC,CAAD,CAAJ,IAAWvS,UAAU,CAAC6N,aAAX,CAAyB0E,IAAI,CAAC,CAAD,CAA7B,CAAX;MACAA,IAAI,CAAC,CAAD,CAAJ,IAAWvS,UAAU,CAAC+N,cAAX,CAA0BwE,IAAI,CAAC,CAAD,CAA9B,CAAX;KAJE,EAKD,IALC,CAAJ;;;;;;;;;;;;SAcFhT,mBAAIM,SAAS;IACXpG,QAAM,CAAC,KAAKoG,OAAN,EAAeA,OAAf,CAAN,CADW;;QAIPA,OAAO,CAACC,WAAZ,EAAyB;WAClBA,WAAL,CAAiBF,MAAjB;;;QAEEC,OAAO,CAACyG,WAAZ,EAAyB;;WAElBhG,KAAL,CAAWuG,OAAX;WACKvG,KAAL,CAAW3G,MAAX,GAAoBkG,OAAO,CAACyG,WAA5B;WACKhG,KAAL,CAAWmG,IAAX;;;WAEK,IAAP;;;;;;;;;;;SAUF+L,qBAAKC,OAAO;SACL/R,OAAL,CAAagS,OAAb,GAAuBD,KAAK,GAAGjB,WAAH,GAAiBD,IAA7C;;;;;;;;;;;SAUFnM,+BAAU2G,WAAW;QACXrL,OADW,GACC,IADD,CACXA,OADW;;QAGfA,OAAO,CAACgS,OAAZ,EAAqB;;KAHF;;;SAQd5S,WAAL,CAAiBO,eAAjB,CAAiC0L,SAAjC;QAEI/L,UAAJ;QACQD,WAXW,GAWK,IAXL,CAWXA,WAXW;;;;QAgBb4S,aAhBa,GAgBKjS,OAhBL,CAgBbiS,aAhBa;;;QAoBf,CAACA,aAAD,IAAmBA,aAAa,IAAIA,aAAa,CAACnF,KAAd,GAAsBT,gBAA9D,EAAiF;MAC/ErM,OAAO,CAACiS,aAAR,GAAwB,IAAxB;MACAA,aAAa,GAAG,IAAhB;;;QAGEnX,CAAC,GAAG,CAAR;;WAEOA,CAAC,GAAGuE,WAAW,CAAC9F,MAAvB,EAA+B;MAC7B+F,UAAU,GAAGD,WAAW,CAACvE,CAAD,CAAxB,CAD6B;;;;;;;UASzBkF,OAAO,CAACgS,OAAR,KAAoBlB,WAApB;OACDmB,aAAD,IAAkB3S,UAAU,KAAK2S,aAAjC;MACA3S,UAAU,CAACkO,gBAAX,CAA4ByE,aAA5B,CAFE,CAAJ,EAE+C;;QAC7C3S,UAAU,CAACoF,SAAX,CAAqB2G,SAArB;OAHF,MAIO;QACL/L,UAAU,CAACwO,KAAX;OAd2B;;;;UAmBzB,CAACmE,aAAD,IAAkB3S,UAAU,CAACwN,KAAX,IAAoBZ,WAAW,GAAGC,aAAd,GAA8BC,WAAlD,CAAtB,EAAsF;QACpFpM,OAAO,CAACiS,aAAR,GAAwB3S,UAAxB;QACA2S,aAAa,GAAG3S,UAAhB;;;MAEFxE,CAAC;;;;;;;;;;;SAUL8R,mBAAItN,YAAY;QACVA,UAAU,YAAYyN,UAA1B,EAAsC;aAC7BzN,UAAP;;;QAGMD,WALM,GAKU,IALV,CAKNA,WALM;;SAOT,IAAIvE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGuE,WAAW,CAAC9F,MAAhC,EAAwCuB,CAAC,EAAzC,EAA6C;UACvCuE,WAAW,CAACvE,CAAD,CAAX,CAAeqE,OAAf,CAAuBsO,KAAvB,KAAiCnO,UAArC,EAAiD;eACxCD,WAAW,CAACvE,CAAD,CAAlB;;;;WAGG,IAAP;;;;;;;;;;SASFkW,mBAAI1R,YAAY;QACVuM,cAAc,CAACvM,UAAD,EAAa,KAAb,EAAoB,IAApB,CAAlB,EAA6C;aACpC,IAAP;KAFY;;;QAMR4S,QAAQ,GAAG,KAAKtF,GAAL,CAAStN,UAAU,CAACH,OAAX,CAAmBsO,KAA5B,CAAjB;;QAEIyE,QAAJ,EAAc;WACPC,MAAL,CAAYD,QAAZ;;;SAGG7S,WAAL,CAAiB0I,IAAjB,CAAsBzI,UAAtB;IACAA,UAAU,CAACX,OAAX,GAAqB,IAArB;SAEKS,WAAL,CAAiBF,MAAjB;WACOI,UAAP;;;;;;;;;;SASF6S,yBAAO7S,YAAY;QACbuM,cAAc,CAACvM,UAAD,EAAa,QAAb,EAAuB,IAAvB,CAAlB,EAAgD;aACvC,IAAP;;;QAGI8S,gBAAgB,GAAG,KAAKxF,GAAL,CAAStN,UAAT,CAAzB,CALiB;;QAQbA,UAAJ,EAAgB;UACND,WADM,GACU,IADV,CACNA,WADM;UAERhG,KAAK,GAAG+M,OAAO,CAAC/G,WAAD,EAAc+S,gBAAd,CAArB;;UAEI/Y,KAAK,KAAK,CAAC,CAAf,EAAkB;QAChBgG,WAAW,CAAC2I,MAAZ,CAAmB3O,KAAnB,EAA0B,CAA1B;aACK+F,WAAL,CAAiBF,MAAjB;;;;WAIG,IAAP;;;;;;;;;;;SAUFmT,iBAAGC,QAAQvN,SAAS;QACduN,MAAM,KAAKpZ,SAAX,IAAwB6L,OAAO,KAAK7L,SAAxC,EAAmD;aAC1C,IAAP;;;QAGM0Y,QALU,GAKG,IALH,CAKVA,QALU;IAOlBjU,IAAI,CAACgH,QAAQ,CAAC2N,MAAD,CAAT,EAAmB,UAAA7E,KAAK,EAAI;MAC9BmE,QAAQ,CAACnE,KAAD,CAAR,GAAkBmE,QAAQ,CAACnE,KAAD,CAAR,IAAmB,EAArC;MACAmE,QAAQ,CAACnE,KAAD,CAAR,CAAgB1F,IAAhB,CAAqBhD,OAArB;KAFE,CAAJ;WAIO,IAAP;;;;;;;;;;SASFwN,mBAAID,QAAQvN,SAAS;QACfuN,MAAM,KAAKpZ,SAAf,EAA0B;aACjB,IAAP;;;QAGM0Y,QALW,GAKE,IALF,CAKXA,QALW;IAOnBjU,IAAI,CAACgH,QAAQ,CAAC2N,MAAD,CAAT,EAAmB,UAAA7E,KAAK,EAAI;UAC1B,CAAC1I,OAAL,EAAc;eACL6M,QAAQ,CAACnE,KAAD,CAAf;OADF,MAEO;QACLmE,QAAQ,CAACnE,KAAD,CAAR,IAAmBmE,QAAQ,CAACnE,KAAD,CAAR,CAAgBzF,MAAhB,CAAuB5B,OAAO,CAACwL,QAAQ,CAACnE,KAAD,CAAT,EAAkB1I,OAAlB,CAA9B,EAA0D,CAA1D,CAAnB;;KAJA,CAAJ;WAOO,IAAP;;;;;;;;;SAQFN,qBAAKgJ,OAAO2D,MAAM;;QAEZ,KAAKjS,OAAL,CAAaiR,SAAjB,EAA4B;MAC1Be,eAAe,CAAC1D,KAAD,EAAQ2D,IAAR,CAAf;KAHc;;;QAOVQ,QAAQ,GAAG,KAAKA,QAAL,CAAcnE,KAAd,KAAwB,KAAKmE,QAAL,CAAcnE,KAAd,EAAqB5S,KAArB,EAAzC;;QAEI,CAAC+W,QAAD,IAAa,CAACA,QAAQ,CAACrY,MAA3B,EAAmC;;;;IAInC6X,IAAI,CAACpM,IAAL,GAAYyI,KAAZ;;IACA2D,IAAI,CAAClR,cAAL,GAAsB,YAAY;MAChCkR,IAAI,CAACvR,QAAL,CAAcK,cAAd;KADF;;QAIIpF,CAAC,GAAG,CAAR;;WAEOA,CAAC,GAAG8W,QAAQ,CAACrY,MAApB,EAA4B;MAC1BqY,QAAQ,CAAC9W,CAAD,CAAR,CAAYsW,IAAZ;MACAtW,CAAC;;;;;;;;;;SASLqL,6BAAU;SACHpH,OAAL,IAAgBgS,cAAc,CAAC,IAAD,EAAO,KAAP,CAA9B;SAEKa,QAAL,GAAgB,EAAhB;SACK5R,OAAL,GAAe,EAAf;SACKJ,KAAL,CAAWuG,OAAX;SACKpH,OAAL,GAAe,IAAf;;;;;;AClVJ,IAAMyT,sBAAsB,GAAG;EAC7B7J,UAAU,EAAE9L,WADiB;EAE7B+L,SAAS,EAAE9L,UAFkB;EAG7B+L,QAAQ,EAAE9L,SAHmB;EAI7B+L,WAAW,EAAE9L;CAJf;AAOA,IAAMyV,0BAA0B,GAAG,YAAnC;AACA,IAAMC,0BAA0B,GAAG,2CAAnC;;;;;;;;IAQqBC;;;;;8BACL;;;QACRxL,KAAK,GAAGwL,gBAAgB,CAACvL,SAA7B;IACAD,KAAK,CAAClB,QAAN,GAAiBwM,0BAAjB;IACAtL,KAAK,CAACjB,KAAN,GAAcwM,0BAAd;+BAESpZ,SAAT;UACKsZ,OAAL,GAAe,KAAf;;;;;;SAGF7N,2BAAQe,IAAI;QACNd,IAAI,GAAGwN,sBAAsB,CAAC1M,EAAE,CAACd,IAAJ,CAAjC,CADU;;QAINA,IAAI,KAAKnI,WAAb,EAA0B;WACnB+V,OAAL,GAAe,IAAf;;;QAGE,CAAC,KAAKA,OAAV,EAAmB;;;;QAIf1J,OAAO,GAAG2J,sBAAsB,CAAC/U,IAAvB,CAA4B,IAA5B,EAAkCgI,EAAlC,EAAsCd,IAAtC,CAAd,CAZU;;QAeNA,IAAI,IAAIjI,SAAS,GAAGC,YAAhB,CAAJ,IAAqCkM,OAAO,CAAC,CAAD,CAAP,CAAW3P,MAAX,GAAoB2P,OAAO,CAAC,CAAD,CAAP,CAAW3P,MAA/B,KAA0C,CAAnF,EAAsF;WAC/EqZ,OAAL,GAAe,KAAf;;;SAGGlN,QAAL,CAAc,KAAK/G,OAAnB,EAA4BqG,IAA5B,EAAkC;MAChC3E,QAAQ,EAAE6I,OAAO,CAAC,CAAD,CADe;MAEhC5E,eAAe,EAAE4E,OAAO,CAAC,CAAD,CAFQ;MAGhCxB,WAAW,EAAElL,gBAHmB;MAIhCqD,QAAQ,EAAEiG;KAJZ;;;;EA7B0CL;AAsC9C;AAOA,SAASoN,sBAAT,CAAgC/M,EAAhC,EAAoCd,IAApC,EAA0C;MACpC8N,GAAG,GAAG7K,OAAO,CAACnC,EAAE,CAACoD,OAAJ,CAAjB;MACI6J,OAAO,GAAG9K,OAAO,CAACnC,EAAE,CAACyD,cAAJ,CAArB;;MAEIvE,IAAI,IAAIjI,SAAS,GAAGC,YAAhB,CAAR,EAAuC;IACrC8V,GAAG,GAAG3K,WAAW,CAAC2K,GAAG,CAACtT,MAAJ,CAAWuT,OAAX,CAAD,EAAsB,YAAtB,EAAoC,IAApC,CAAjB;;;SAGK,CAACD,GAAD,EAAMC,OAAN,CAAP;;;AChFF;;;;;;;;AAQA,AAAe,SAASC,SAAT,CAAmBC,MAAnB,EAA2BhC,IAA3B,EAAiCiC,OAAjC,EAA0C;MACnDC,kBAAkB,2BAAyBlC,IAAzB,UAAkCiC,OAAlC,WAAtB;SACO,YAAW;QACZE,CAAC,GAAG,IAAIC,KAAJ,CAAU,iBAAV,CAAR;QACIC,KAAK,GAAGF,CAAC,IAAIA,CAAC,CAACE,KAAP,GAAeF,CAAC,CAACE,KAAF,CAAQ7L,OAAR,CAAgB,iBAAhB,EAAmC,EAAnC,EACtBA,OADsB,CACd,aADc,EACC,EADD,EAEtBA,OAFsB,CAEd,4BAFc,EAEgB,gBAFhB,CAAf,GAEmD,qBAF/D;QAII8L,GAAG,GAAGvY,MAAM,CAACwY,OAAP,KAAmBxY,MAAM,CAACwY,OAAP,CAAeC,IAAf,IAAuBzY,MAAM,CAACwY,OAAP,CAAeD,GAAzD,CAAV;;QACIA,GAAJ,EAAS;MACPA,GAAG,CAACzV,IAAJ,CAAS9C,MAAM,CAACwY,OAAhB,EAAyBL,kBAAzB,EAA6CG,KAA7C;;;WAEKL,MAAM,CAAChV,KAAP,CAAa,IAAb,EAAmB3E,SAAnB,CAAP;GAVF;;;ACTF;;;;;;;;;;AASA,IAAMoa,MAAM,GAAGV,SAAS,CAAC,UAACW,IAAD,EAAOtN,GAAP,EAAYuN,KAAZ,EAAsB;MACzCC,IAAI,GAAG7a,MAAM,CAAC6a,IAAP,CAAYxN,GAAZ,CAAX;MACIvL,CAAC,GAAG,CAAR;;SACOA,CAAC,GAAG+Y,IAAI,CAACta,MAAhB,EAAwB;QAClB,CAACqa,KAAD,IAAWA,KAAK,IAAID,IAAI,CAACE,IAAI,CAAC/Y,CAAD,CAAL,CAAJ,KAAkB5B,SAA1C,EAAsD;MACpDya,IAAI,CAACE,IAAI,CAAC/Y,CAAD,CAAL,CAAJ,GAAgBuL,GAAG,CAACwN,IAAI,CAAC/Y,CAAD,CAAL,CAAnB;;;IAEFA,CAAC;;;SAEI6Y,IAAP;CATsB,EAUrB,QAVqB,EAUX,eAVW,CAAxB;;ACRA;;;;;;;;;AAQA,IAAMC,KAAK,GAAGZ,SAAS,CAAC,UAACW,IAAD,EAAOtN,GAAP,EAAe;SAC9BqN,MAAM,CAACC,IAAD,EAAOtN,GAAP,EAAY,IAAZ,CAAb;CADqB,EAEpB,OAFoB,EAEX,eAFW,CAAvB;;ACTA;;;;;;;;AAOA,AAAe,SAASyN,OAAT,CAAiBC,KAAjB,EAAwBC,IAAxB,EAA8BC,UAA9B,EAA0C;MACnDC,KAAK,GAAGF,IAAI,CAAC5M,SAAjB;MACI+M,MAAJ;EAEAA,MAAM,GAAGJ,KAAK,CAAC3M,SAAN,GAAkBpO,MAAM,CAACob,MAAP,CAAcF,KAAd,CAA3B;EACAC,MAAM,CAACE,WAAP,GAAqBN,KAArB;EACAI,MAAM,CAACG,MAAP,GAAgBJ,KAAhB;;MAEID,UAAJ,EAAgB;IACdlb,QAAM,CAACob,MAAD,EAASF,UAAT,CAAN;;;;ACjBJ;;;;;;;AAOA,AAAe,SAASM,MAAT,CAAgBxI,EAAhB,EAAoBlO,OAApB,EAA6B;SACnC,SAAS2W,OAAT,GAAmB;WACjBzI,EAAE,CAAC9N,KAAH,CAASJ,OAAT,EAAkBvE,SAAlB,CAAP;GADF;;;ACkDF;;;;;;;;IAOqBmb;;;MAAAA;;;;;kBA6DR1V,OAAZ,EAAqBI,OAArB,EAAmC;QAAdA,OAAc;MAAdA,OAAc,GAAJ,EAAI;;;WAC3B,IAAIuS,OAAJ,CAAY3S,OAAZ;MACNM,WAAW,EAEHuR,MAFG;OAIRzR,OALG,EAAP;;;EA9DmBsV,OAKbC,UAAU;EALGD,OAMbjX,gBAAgBA;EANHiX,OAObpX,iBAAiBA;EAPJoX,OAQbvX,iBAAiBA;EARJuX,OASbtX,kBAAkBA;EATLsX,OAUbrX,eAAeA;EAVFqX,OAWbnX,uBAAuBA;EAXVmX,OAYblX,qBAAqBA;EAZRkX,OAabxX,iBAAiBA;EAbJwX,OAcbpX,iBAAiBA;EAdJoX,OAeb5X,cAAcA;EAfD4X,OAgBb3X,aAAaA;EAhBA2X,OAiBZ1X,YAAYA;EAjBA0X,OAkBbzX,eAAeA;EAlBFyX,OAmBbxI,iBAAiBA;EAnBJwI,OAoBbvI,cAAcA;EApBDuI,OAqBbtI,gBAAgBA;EArBHsI,OAsBbrI,cAAcA;EAtBDqI,OAuBbpI,mBAAmBA;EAvBNoI,OAwBbnI,kBAAkBA;EAxBLmI,OAyBblI,eAAeA;EAzBFkI,OA0Bb/C,UAAUA;EA1BG+C,OA2BbhP,QAAQA;EA3BKgP,OA4Bb/V,cAAcA;EA5BD+V,OA6BbzL,aAAaA;EA7BAyL,OA8BbxK,aAAaA;EA9BAwK,OA+BbvN,oBAAoBA;EA/BPuN,OAgCbtJ,kBAAkBA;EAhCLsJ,OAiCb9B,mBAAmBA;EAjCN8B,OAkCb1H,aAAaA;EAlCA0H,OAmCbtF,iBAAiBA;EAnCJsF,OAoCbE,MAAM3G;EApCOyG,OAqCbG,MAAMnF;EArCOgF,OAsCbI,QAAQ/E;EAtCK2E,OAuCbK,QAAQ/E;EAvCK0E,OAwCbM,SAAS9E;EAxCIwE,OAyCbO,QAAQ9E;EAzCKuE,OA0CbpC,KAAKxN;EA1CQ4P,OA2CblC,MAAMrN;EA3COuP,OA4Cb9W,OAAOA;EA5CM8W,OA6Cbb,QAAQA;EA7CKa,OA8Cbf,SAASA;EA9CIe,OA+CbF,SAASA;EA/CIE,OAgDb1b,SAASA;EAhDI0b,OAiDbX,UAAUA;EAjDGW,OAkDbF,SAASA;EAlDIE,OAmDbna,WAAWA;EAnDEma,OAoDbxM,UAAUA;EApDGwM,OAqDbrO,UAAUA;EArDGqO,OAsDbtM,cAAcA;EAtDDsM,OAuDb9P,WAAWA;EAvDE8P,OAwDb1W,WAAWA;EAxDE0W,OAyDb9T,YAAYA;EAzDC8T,OA0Db5P,oBAAoBA;EA1DP4P,OA2DbvP,uBAAuBA;EA3DVuP,OA4Db9C,WAAW5Y,QAAM,CAAC,EAAD,EAAK4Y,QAAL,EAAe;IAAEf,MAAM,EAANA;GAAjB;SA5DJ6D;;;ACHrB;;AAEA,IAAM9C,UAAQ,GAAG8C,MAAM,CAAC9C,QAAxB;;;;;"}