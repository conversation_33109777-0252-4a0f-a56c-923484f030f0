var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.default=void 0;var _slicedToArray2=_interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));var _objectWithoutProperties2=_interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));var _react=_interopRequireWildcard(require("react"));var _reactNative=require("react-native");var _index=_interopRequireDefault(require("./index"));var _StepsIndicator=require("./components/StepsIndicator");var _styles=require("./utils/styles");var _constants=require("./utils/constants");var _jsxRuntime=require("react/jsx-runtime");var _excluded=["onValueChange","onSlidingStart","onSlidingComplete","onAccessibilityAction","value","minimumValue","maximumValue","step","inverted","tapToSeek","lowerLimit","upperLimit"];var _this=this,_jsxFileName="/Users/<USER>/Desktop/Projects/react-native-slider/package/src/Slider.tsx";function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap(),t=new WeakMap();return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?t:r;})(e);}function _interopRequireWildcard(e,r){if(!r&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=_getRequireWildcardCache(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&{}.hasOwnProperty.call(e,u)){var i=a?Object.getOwnPropertyDescriptor(e,u):null;i&&(i.get||i.set)?Object.defineProperty(n,u,i):n[u]=e[u];}return n.default=e,t&&t.set(e,n),n;}var SliderComponent=function SliderComponent(_ref,forwardedRef){var _ref2,_props$accessibilityS;var onValueChange=_ref.onValueChange,onSlidingStart=_ref.onSlidingStart,onSlidingComplete=_ref.onSlidingComplete,onAccessibilityAction=_ref.onAccessibilityAction,_ref$value=_ref.value,value=_ref$value===void 0?_constants.constants.SLIDER_DEFAULT_INITIAL_VALUE:_ref$value,_ref$minimumValue=_ref.minimumValue,minimumValue=_ref$minimumValue===void 0?0:_ref$minimumValue,_ref$maximumValue=_ref.maximumValue,maximumValue=_ref$maximumValue===void 0?1:_ref$maximumValue,_ref$step=_ref.step,step=_ref$step===void 0?0:_ref$step,_ref$inverted=_ref.inverted,inverted=_ref$inverted===void 0?false:_ref$inverted,_ref$tapToSeek=_ref.tapToSeek,tapToSeek=_ref$tapToSeek===void 0?false:_ref$tapToSeek,_ref$lowerLimit=_ref.lowerLimit,lowerLimit=_ref$lowerLimit===void 0?_reactNative.Platform.select({web:minimumValue,default:_constants.constants.LIMIT_MIN_VALUE}):_ref$lowerLimit,_ref$upperLimit=_ref.upperLimit,upperLimit=_ref$upperLimit===void 0?_reactNative.Platform.select({web:maximumValue,default:_constants.constants.LIMIT_MAX_VALUE}):_ref$upperLimit,props=(0,_objectWithoutProperties2.default)(_ref,_excluded);var _useState=(0,_react.useState)((_ref2=value!=null?value:minimumValue)!=null?_ref2:_constants.constants.SLIDER_DEFAULT_INITIAL_VALUE),_useState2=(0,_slicedToArray2.default)(_useState,2),currentValue=_useState2[0],setCurrentValue=_useState2[1];var _useState3=(0,_react.useState)(0),_useState4=(0,_slicedToArray2.default)(_useState3,2),width=_useState4[0],setWidth=_useState4[1];var stepResolution=step?step:_constants.constants.DEFAULT_STEP_RESOLUTION;var defaultStep=(maximumValue-minimumValue)/stepResolution;var stepLength=step||defaultStep;var options=Array.from({length:(step?defaultStep:stepResolution)+1},function(_,index){return minimumValue+index*stepLength;});var defaultStyle=_reactNative.Platform.OS==='ios'?_styles.styles.defaultSlideriOS:_styles.styles.defaultSlider;var sliderStyle={zIndex:1,width:width};var style=[defaultStyle,props.style];var onValueChangeEvent=function onValueChangeEvent(event){onValueChange&&onValueChange(event.nativeEvent.value);setCurrentValue(event.nativeEvent.value);};var _disabled=typeof props.disabled==='boolean'?props.disabled:((_props$accessibilityS=props.accessibilityState)==null?void 0:_props$accessibilityS.disabled)===true;var _accessibilityState=typeof props.disabled==='boolean'?Object.assign({},props.accessibilityState,{disabled:props.disabled}):props.accessibilityState;var onSlidingStartEvent=onSlidingStart?function(event){onSlidingStart(event.nativeEvent.value);}:null;var onSlidingCompleteEvent=onSlidingComplete?function(event){onSlidingComplete(event.nativeEvent.value);}:null;var onAccessibilityActionEvent=onAccessibilityAction?function(event){onAccessibilityAction(event);}:null;var passedValue=Number.isNaN(value)||!value?undefined:value;(0,_react.useEffect)(function(){if(lowerLimit>=upperLimit){console.warn('Invalid configuration: lower limit is supposed to be smaller than upper limit');}},[lowerLimit,upperLimit]);return(0,_jsxRuntime.jsxs)(_reactNative.View,{onLayout:function onLayout(event){setWidth(event.nativeEvent.layout.width);},style:[style,{justifyContent:'center'}],children:[props.StepMarker||!!props.renderStepNumber?(0,_jsxRuntime.jsx)(_StepsIndicator.StepsIndicator,{options:options,sliderWidth:width,currentValue:currentValue,renderStepNumber:props.renderStepNumber,thumbImage:props.thumbImage,StepMarker:props.StepMarker,isLTR:inverted}):null,(0,_jsxRuntime.jsx)(_index.default,Object.assign({},props,{minimumValue:minimumValue,maximumValue:maximumValue,step:step,inverted:inverted,tapToSeek:tapToSeek,value:passedValue,lowerLimit:lowerLimit,upperLimit:upperLimit,accessibilityState:_accessibilityState,thumbImage:_reactNative.Platform.OS==='web'?props.thumbImage:props.StepMarker?undefined:_reactNative.Image.resolveAssetSource(props.thumbImage),ref:forwardedRef,style:[sliderStyle,defaultStyle,{alignContent:'center',alignItems:'center'}],onChange:onValueChangeEvent,onRNCSliderSlidingStart:onSlidingStartEvent,onRNCSliderSlidingComplete:onSlidingCompleteEvent,onRNCSliderValueChange:onValueChangeEvent,disabled:_disabled,onStartShouldSetResponder:function onStartShouldSetResponder(){return true;},onResponderTerminationRequest:function onResponderTerminationRequest(){return false;},onRNCSliderAccessibilityAction:onAccessibilityActionEvent,thumbTintColor:props.thumbImage&&!!props.StepMarker?'transparent':props.thumbTintColor}))]});};var SliderWithRef=_react.default.forwardRef(SliderComponent);var _default=exports.default=SliderWithRef;