var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");var React=_interopRequireWildcard(require("react"));var _reactTestRenderer=_interopRequireDefault(require("react-test-renderer"));var _Slider=_interopRequireDefault(require("../Slider"));var _reactNative=require("react-native");var _jsxRuntime=require("react/jsx-runtime");var _this=this,_jsxFileName="/Users/<USER>/Desktop/Projects/react-native-slider/package/src/__tests__/Slider.test.tsx";function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap(),t=new WeakMap();return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?t:r;})(e);}function _interopRequireWildcard(e,r){if(!r&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=_getRequireWildcardCache(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&{}.hasOwnProperty.call(e,u)){var i=a?Object.getOwnPropertyDescriptor(e,u):null;i&&(i.get||i.set)?Object.defineProperty(n,u,i):n[u]=e[u];}return n.default=e,t&&t.set(e,n),n;}describe('<Slider />',function(){it('renders enabled slider',function(){var tree=_reactTestRenderer.default.create((0,_jsxRuntime.jsx)(_Slider.default,{})).toJSON();expect(tree).toMatchSnapshot();});it('renders disabled slider',function(){var tree=_reactTestRenderer.default.create((0,_jsxRuntime.jsx)(_Slider.default,{disabled:true})).toJSON();expect(tree).toMatchSnapshot();});it('accessibilityState disabled sets disabled={true}',function(){var tree=_reactTestRenderer.default.create((0,_jsxRuntime.jsx)(_Slider.default,{accessibilityState:{disabled:true}})).toJSON();expect(tree).toMatchSnapshot();});it('disabled prop overrides accessibilityState.disabled',function(){var tree=_reactTestRenderer.default.create((0,_jsxRuntime.jsx)(_Slider.default,{disabled:true,accessibilityState:{disabled:false}})).toJSON();expect(tree).toMatchSnapshot();});it('disabled prop overrides accessibilityState.enabled',function(){var tree=_reactTestRenderer.default.create((0,_jsxRuntime.jsx)(_Slider.default,{disabled:false,accessibilityState:{disabled:true}})).toJSON();expect(tree).toMatchSnapshot();});it('renders a slider with custom props',function(){var tree=_reactTestRenderer.default.create((0,_jsxRuntime.jsx)(_Slider.default,{value:0.5,minimumValue:-1,maximumValue:2,step:0.25,minimumTrackTintColor:'blue',maximumTrackTintColor:'red',thumbTintColor:'green',onSlidingComplete:function onSlidingComplete(){},onValueChange:function onValueChange(){},lowerLimit:0,upperLimit:1})).toJSON();expect(tree).toMatchSnapshot();});it('renders a slider with custom stepMaker',function(){var tree=_reactTestRenderer.default.create((0,_jsxRuntime.jsx)(_Slider.default,{value:2,minimumValue:0,maximumValue:4,StepMarker:function StepMarker(_ref){var stepMarked=_ref.stepMarked;return stepMarked?(0,_jsxRuntime.jsx)(_reactNative.View,{children:(0,_jsxRuntime.jsx)(_reactNative.View,{style:{width:10,backgroundColor:'red'}})}):(0,_jsxRuntime.jsx)(_reactNative.View,{children:(0,_jsxRuntime.jsx)(_reactNative.View,{style:{width:10,backgroundColor:'green'}})});}})).toJSON();expect(tree).toMatchSnapshot();});});