{"version": 3, "names": ["_reactNative", "require", "_utils", "useNewWebImplementation", "getWasCalled", "enableExperimentalWebImplementation", "_shouldEnable", "console", "warn", "tagMessage", "enableLegacyWebImplementation", "shouldUseLegacyImplementation", "Platform", "OS", "error", "isNewWebImplementationEnabled"], "sourceRoot": "../../src", "sources": ["EnableNewWebImplementation.ts"], "mappings": ";;;;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAEA,IAAIE,uBAAuB,GAAG,IAAI;AAClC,IAAIC,YAAY,GAAG,KAAK;;AAExB;AACA;AACA;AACO,SAASC,mCAAmCA,CACjDC,aAAa,GAAG,IAAI,EACd;EACN;EACAC,OAAO,CAACC,IAAI,CACV,IAAAC,iBAAU,EACR,mGACF,CACF,CAAC;AACH;;AAEA;AACA;AACA;AACO,SAASC,6BAA6BA,CAC3CC,6BAA6B,GAAG,IAAI,EAC9B;EACNJ,OAAO,CAACC,IAAI,CACV,IAAAC,iBAAU,EACR,8FACF,CACF,CAAC;EAED,IACEG,qBAAQ,CAACC,EAAE,KAAK,KAAK,IACrBV,uBAAuB,KAAK,CAACQ,6BAA6B,EAC1D;IACA;EACF;EAEA,IAAIP,YAAY,EAAE;IAChBG,OAAO,CAACO,KAAK,CACX,mLACF,CAAC;IACD;EACF;EAEAX,uBAAuB,GAAG,CAACQ,6BAA6B;AAC1D;AAEO,SAASI,6BAA6BA,CAAA,EAAY;EACvDX,YAAY,GAAG,IAAI;EACnB,OAAOD,uBAAuB;AAChC", "ignoreList": []}