{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_createNativeWrapper", "_interopRequireDefault", "_NativeViewGestureHandler", "_utils", "_jsxRuntime", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "RefreshControl", "exports", "createNativeWrapper", "RNRefreshControl", "disallowInterruption", "shouldCancelWhenOutside", "GHScrollView", "RNScrollView", "ScrollView", "forwardRef", "props", "ref", "refreshControlGestureRef", "useRef", "refreshControl", "waitFor", "rest", "jsx", "toArray", "cloneElement", "undefined", "Switch", "RNSwitch", "shouldActivateOnStart", "TextInput", "RNTextInput", "DrawerLayoutAndroid", "RNDrawerLayoutAndroid", "FlatList", "flatListProps", "scrollViewProps", "propName", "value", "entries", "nativeViewProps", "includes", "renderScrollComponent", "scrollProps"], "sourceRoot": "../../../src", "sources": ["components/GestureComponents.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AAOA,IAAAC,YAAA,GAAAD,OAAA;AAcA,IAAAE,oBAAA,GAAAC,sBAAA,CAAAH,OAAA;AAEA,IAAAI,yBAAA,GAAAJ,OAAA;AAKA,IAAAK,MAAA,GAAAL,OAAA;AAAmC,IAAAM,WAAA,GAAAN,OAAA;AAAA,SAAAG,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAZ,uBAAA,YAAAA,CAAAQ,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAE5B,MAAMgB,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAG,IAAAE,4BAAmB,EAACC,2BAAgB,EAAE;EAClEC,oBAAoB,EAAE,IAAI;EAC1BC,uBAAuB,EAAE;AAC3B,CAAC,CAAC;AACF;;AAGA,MAAMC,YAAY,GAAG,IAAAJ,4BAAmB,EACtCK,uBAAY,EACZ;EACEH,oBAAoB,EAAE,IAAI;EAC1BC,uBAAuB,EAAE;AAC3B,CACF,CAAC;AACM,MAAMG,UAAU,GAAAP,OAAA,CAAAO,UAAA,gBAAGpC,KAAK,CAACqC,UAAU,CAGxC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAChB,MAAMC,wBAAwB,GAAGxC,KAAK,CAACyC,MAAM,CAAiB,IAAI,CAAC;EACnE,MAAM;IAAEC,cAAc;IAAEC,OAAO;IAAE,GAAGC;EAAK,CAAC,GAAGN,KAAK;EAElD,oBACE,IAAA9B,WAAA,CAAAqC,GAAA,EAACX,YAAY;IAAA,GACPU,IAAI;IACR;IACAL,GAAG,EAAEA,GAAI;IACTI,OAAO,EAAE,CAAC,GAAG,IAAAG,cAAO,EAACH,OAAO,IAAI,EAAE,CAAC,EAAEH,wBAAwB;IAC7D;IAAA;IACAE,cAAc,EACZA,cAAc,gBACV1C,KAAK,CAAC+C,YAAY,CAACL,cAAc,EAAE;MACjC;MACAH,GAAG,EAAEC;IACP,CAAC,CAAC,GACFQ;EACL,CACF,CAAC;AAEN,CAAC,CAAC;AACF;AACA;AACA;;AAGO,MAAMC,MAAM,GAAApB,OAAA,CAAAoB,MAAA,GAAG,IAAAnB,4BAAmB,EAAgBoB,mBAAQ,EAAE;EACjEjB,uBAAuB,EAAE,KAAK;EAC9BkB,qBAAqB,EAAE,IAAI;EAC3BnB,oBAAoB,EAAE;AACxB,CAAC,CAAC;AACF;;AAGO,MAAMoB,SAAS,GAAAvB,OAAA,CAAAuB,SAAA,GAAG,IAAAtB,4BAAmB,EAAmBuB,sBAAW,CAAC;AAC3E;;AAGO,MAAMC,mBAAmB,GAAAzB,OAAA,CAAAyB,mBAAA,GAAG,IAAAxB,4BAAmB,EAEpDyB,gCAAqB,EAAE;EAAEvB,oBAAoB,EAAE;AAAK,CAAC,CAAC;AACxD;;AAIO,MAAMwB,QAAQ,GAAA3B,OAAA,CAAA2B,QAAA,gBAAGxD,KAAK,CAACqC,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EACvD,MAAMC,wBAAwB,GAAGxC,KAAK,CAACyC,MAAM,CAAiB,IAAI,CAAC;EAEnE,MAAM;IAAEE,OAAO;IAAED,cAAc;IAAE,GAAGE;EAAK,CAAC,GAAGN,KAAK;EAElD,MAAMmB,aAAa,GAAG,CAAC,CAAC;EACxB,MAAMC,eAAe,GAAG,CAAC,CAAC;EAC1B,KAAK,MAAM,CAACC,QAAQ,EAAEC,KAAK,CAAC,IAAInC,MAAM,CAACoC,OAAO,CAACjB,IAAI,CAAC,EAAE;IACpD;IACA,IAAKkB,yCAAe,CAAuBC,QAAQ,CAACJ,QAAQ,CAAC,EAAE;MAC7D;MACA;MACAD,eAAe,CAACC,QAAQ,CAAC,GAAGC,KAAK;IACnC,CAAC,MAAM;MACL;MACA;MACAH,aAAa,CAACE,QAAQ,CAAC,GAAGC,KAAK;IACjC;EACF;EAEA;IAAA;IACE;IACA,IAAApD,WAAA,CAAAqC,GAAA,EAAC1C,YAAA,CAAAqD,QAAU;MACTjB,GAAG,EAAEA,GAAI;MAAA,GACLkB,aAAa;MACjBO,qBAAqB,EAAGC,WAAW,iBACjC,IAAAzD,WAAA,CAAAqC,GAAA,EAACT,UAAU;QAEP,GAAG6B,WAAW;QACd,GAAGP,eAAe;QAClBf,OAAO,EAAE,CAAC,GAAG,IAAAG,cAAO,EAACH,OAAO,IAAI,EAAE,CAAC,EAAEH,wBAAwB;MAAC,CAEjE;MAEH;MAAA;MACAE,cAAc,EACZA,cAAc,gBACV1C,KAAK,CAAC+C,YAAY,CAACL,cAAc,EAAE;QACjC;QACAH,GAAG,EAAEC;MACP,CAAC,CAAC,GACFQ;IACL,CACF;EAAC;AAEN,CAAC,CAOuB;AACxB", "ignoreList": []}