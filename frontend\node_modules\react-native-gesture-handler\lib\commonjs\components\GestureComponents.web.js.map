{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_createNativeWrapper", "_interopRequireDefault", "_jsxRuntime", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "ScrollView", "exports", "createNativeWrapper", "RNScrollView", "disallowInterruption", "Switch", "RNSwitch", "shouldCancelWhenOutside", "shouldActivateOnStart", "TextInput", "RNTextInput", "DrawerLayoutAndroid", "console", "warn", "jsx", "View", "RefreshControl", "FlatList", "forwardRef", "props", "ref", "renderScrollComponent", "scrollProps"], "sourceRoot": "../../../src", "sources": ["components/GestureComponents.web.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AASA,IAAAE,oBAAA,GAAAC,sBAAA,CAAAH,OAAA;AAAkE,IAAAI,WAAA,GAAAJ,OAAA;AAAA,SAAAG,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAN,wBAAAM,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAV,uBAAA,YAAAA,CAAAM,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAE3D,MAAMgB,UAAU,GAAAC,OAAA,CAAAD,UAAA,GAAG,IAAAE,4BAAmB,EAACC,uBAAY,EAAE;EAC1DC,oBAAoB,EAAE;AACxB,CAAC,CAAC;AAEK,MAAMC,MAAM,GAAAJ,OAAA,CAAAI,MAAA,GAAG,IAAAH,4BAAmB,EAACI,mBAAQ,EAAE;EAClDC,uBAAuB,EAAE,KAAK;EAC9BC,qBAAqB,EAAE,IAAI;EAC3BJ,oBAAoB,EAAE;AACxB,CAAC,CAAC;AACK,MAAMK,SAAS,GAAAR,OAAA,CAAAQ,SAAA,GAAG,IAAAP,4BAAmB,EAACQ,sBAAW,CAAC;AAClD,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EACvCC,OAAO,CAACC,IAAI,CAAC,8CAA8C,CAAC;EAC5D,oBAAO,IAAAjC,WAAA,CAAAkC,GAAA,EAACrC,YAAA,CAAAsC,IAAI,IAAE,CAAC;AACjB,CAAC;;AAED;AACA;AACA;AAAAd,OAAA,CAAAU,mBAAA,GAAAA,mBAAA;AACO,MAAMK,cAAc,GAAAf,OAAA,CAAAe,cAAA,GAAG,IAAAd,4BAAmB,EAACa,iBAAI,CAAC;AAEhD,MAAME,QAAQ,GAAAhB,OAAA,CAAAgB,QAAA,gBAAG3C,KAAK,CAAC4C,UAAU,CACtC,CAASC,KAA2B,EAAEC,GAAQ,kBAC5C,IAAAxC,WAAA,CAAAkC,GAAA,EAACrC,YAAA,CAAAwC,QAAU;EACTG,GAAG,EAAEA,GAAI;EAAA,GACLD,KAAK;EACTE,qBAAqB,EAAGC,WAAW,iBAAK,IAAA1C,WAAA,CAAAkC,GAAA,EAACd,UAAU;IAAA,GAAKsB;EAAW,CAAG;AAAE,CACzE,CAEL,CAAC", "ignoreList": []}