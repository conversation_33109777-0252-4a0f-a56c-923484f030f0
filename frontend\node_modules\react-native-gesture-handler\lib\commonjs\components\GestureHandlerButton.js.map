{"version": 3, "names": ["_RNGestureHandlerButtonNativeComponent", "_interopRequireDefault", "require", "e", "__esModule", "default", "_default", "exports", "RNGestureHandlerButtonNativeComponent"], "sourceRoot": "../../../src", "sources": ["components/GestureHandlerButton.tsx"], "mappings": ";;;;;;AAEA,IAAAA,sCAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAmG,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,IAAAG,QAAA,GAAAC,OAAA,CAAAF,OAAA,GAEpFG,8CAAqC", "ignoreList": []}