{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_init", "_GestureHandlerRootViewContext", "_interopRequireDefault", "_jsxRuntime", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "GestureHandlerRootView", "style", "rest", "maybeInitializeFabric", "jsx", "Provider", "value", "children", "View", "styles", "container", "StyleSheet", "create", "flex"], "sourceRoot": "../../../src", "sources": ["components/GestureHandlerRootView.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,8BAAA,GAAAC,sBAAA,CAAAJ,OAAA;AAA6E,IAAAK,WAAA,GAAAL,OAAA;AAAA,SAAAI,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAP,wBAAAO,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAX,uBAAA,YAAAA,CAAAO,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAK9D,SAASgB,sBAAsBA,CAAC;EAC7CC,KAAK;EACL,GAAGC;AACwB,CAAC,EAAE;EAC9B;EACA;EACA;EACA,IAAAC,2BAAqB,EAAC,CAAC;EAEvB,oBACE,IAAAvB,WAAA,CAAAwB,GAAA,EAAC1B,8BAAA,CAAAK,OAA6B,CAACsB,QAAQ;IAACC,KAAK;IAAAC,QAAA,eAC3C,IAAA3B,WAAA,CAAAwB,GAAA,EAAC5B,YAAA,CAAAgC,IAAI;MAACP,KAAK,EAAEA,KAAK,IAAIQ,MAAM,CAACC,SAAU;MAAA,GAAKR;IAAI,CAAG;EAAC,CACd,CAAC;AAE7C;AAEA,MAAMO,MAAM,GAAGE,uBAAU,CAACC,MAAM,CAAC;EAC/BF,SAAS,EAAE;IAAEG,IAAI,EAAE;EAAE;AACvB,CAAC,CAAC", "ignoreList": []}