{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_hoistNonReactStatics", "_interopRequireDefault", "_GestureHandlerRootView", "_jsxRuntime", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "gestureHandlerRootHOC", "Component", "containerStyles", "Wrapper", "props", "jsx", "style", "styles", "container", "children", "displayName", "name", "hoistNonReactStatics", "StyleSheet", "create", "flex"], "sourceRoot": "../../../src", "sources": ["components/gestureHandlerRootHOC.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,qBAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,uBAAA,GAAAD,sBAAA,CAAAH,OAAA;AAA8D,IAAAK,WAAA,GAAAL,OAAA;AAAA,SAAAG,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAP,wBAAAO,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAX,uBAAA,YAAAA,CAAAO,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAE/C,SAASgB,qBAAqBA,CAC3CC,SAAiC,EACjCC,eAAsC,EACd;EACxB,SAASC,OAAOA,CAACC,KAAQ,EAAE;IACzB,oBACE,IAAAxB,WAAA,CAAAyB,GAAA,EAAC1B,uBAAA,CAAAI,OAAsB;MAACuB,KAAK,EAAE,CAACC,MAAM,CAACC,SAAS,EAAEN,eAAe,CAAE;MAAAO,QAAA,eACjE,IAAA7B,WAAA,CAAAyB,GAAA,EAACJ,SAAS;QAAA,GAAKG;MAAK,CAAG;IAAC,CACF,CAAC;EAE7B;EAEAD,OAAO,CAACO,WAAW,GAAG,yBACpBT,SAAS,CAACS,WAAW,IAAIT,SAAS,CAACU,IAAI,GACtC;;EAEH;EACA,IAAAC,6BAAoB,EAACT,OAAO,EAAEF,SAAS,CAAC;EAExC,OAAOE,OAAO;AAChB;AAEA,MAAMI,MAAM,GAAGM,uBAAU,CAACC,MAAM,CAAC;EAC/BN,SAAS,EAAE;IAAEO,IAAI,EAAE;EAAE;AACvB,CAAC,CAAC", "ignoreList": []}