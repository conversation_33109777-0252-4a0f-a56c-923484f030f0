{"version": 3, "names": ["_getNextHandlerTag", "require", "_utils", "CALLBACK_TYPE", "exports", "UNDEFINED", "BEGAN", "START", "UPDATE", "CHANGE", "END", "FINALIZE", "TOUCHES_DOWN", "TOUCHES_MOVE", "TOUCHES_UP", "TOUCHES_CANCELLED", "Gesture", "nextGestureId", "BaseGesture", "gestureId", "handlerTag", "handler<PERSON>ame", "config", "handlers", "isWorklet", "constructor", "addDependency", "key", "gesture", "value", "Array", "concat", "with<PERSON>ef", "ref", "callback", "__workletHash", "undefined", "onBegin", "onStart", "onEnd", "onFinalize", "onTouchesDown", "needsPointerData", "onTouchesMove", "onTouchesUp", "onTouchesCancelled", "enabled", "shouldCancelWhenOutside", "hitSlop", "activeCursor", "mouseButton", "runOnJS", "simultaneousWithExternalGesture", "gestures", "requireExternalGestureToFail", "blocksExternalGesture", "withTestId", "id", "testId", "cancelsTouchesInView", "initialize", "getNextHandlerTag", "current", "toGestureArray", "prepare", "shouldUseReanimated", "includes", "isRemoteDebuggingEnabled", "ContinousBaseGesture", "onUpdate", "onChange", "manualActivation"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/gesture.ts"], "mappings": ";;;;;;AASA,IAAAA,kBAAA,GAAAC,OAAA;AAaA,IAAAC,MAAA,GAAAD,OAAA;AAmBsD;;AA8C/C,MAAME,aAAa,GAAAC,OAAA,CAAAD,aAAA,GAAG;EAC3BE,SAAS,EAAE,CAAC;EACZC,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE,CAAC;EACTC,GAAG,EAAE,CAAC;EACNC,QAAQ,EAAE,CAAC;EACXC,YAAY,EAAE,CAAC;EACfC,YAAY,EAAE,CAAC;EACfC,UAAU,EAAE,CAAC;EACbC,iBAAiB,EAAE;AACrB,CAAU;;AAEV;AACA;;AAGO,MAAeC,OAAO,CAAC;AAkB7BZ,OAAA,CAAAY,OAAA,GAAAA,OAAA;AAED,IAAIC,aAAa,GAAG,CAAC;AACd,MAAeC,WAAW,SAEvBF,OAAO,CAAC;EACRG,SAAS,GAAG,CAAC,CAAC;EACfC,UAAU,GAAG,CAAC,CAAC;EACfC,WAAW,GAAG,EAAE;EAChBC,MAAM,GAAsB,CAAC,CAAC;EAC9BC,QAAQ,GAAoC;IACjDJ,SAAS,EAAE,CAAC,CAAC;IACbC,UAAU,EAAE,CAAC,CAAC;IACdI,SAAS,EAAE;EACb,CAAC;EAEDC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;;IAEP;IACA;IACA;IACA;IACA;IACA,IAAI,CAACN,SAAS,GAAGF,aAAa,EAAE;IAChC,IAAI,CAACM,QAAQ,CAACJ,SAAS,GAAG,IAAI,CAACA,SAAS;EAC1C;EAEQO,aAAaA,CACnBC,GAA4D,EAC5DC,OAAoC,EACpC;IACA,MAAMC,KAAK,GAAG,IAAI,CAACP,MAAM,CAACK,GAAG,CAAC;IAC9B,IAAI,CAACL,MAAM,CAACK,GAAG,CAAC,GAAGE,KAAK,GACpBC,KAAK,CAAa,CAAC,CAACC,MAAM,CAACF,KAAK,EAAED,OAAO,CAAC,GAC1C,CAACA,OAAO,CAAC;EACf;;EAEA;AACF;AACA;AACA;EACEI,OAAOA,CAACC,GAAoD,EAAE;IAC5D,IAAI,CAACX,MAAM,CAACW,GAAG,GAAGA,GAAG;IACrB,OAAO,IAAI;EACb;;EAEA;EACUT,SAASA,CAACU,QAAkB,EAAE;IACtC;IACA,OAAOA,QAAQ,CAACC,aAAa,KAAKC,SAAS;EAC7C;;EAEA;AACF;AACA;AACA;AACA;EACEC,OAAOA,CAACH,QAAiE,EAAE;IACzE,IAAI,CAACX,QAAQ,CAACc,OAAO,GAAGH,QAAQ;IAChC,IAAI,CAACX,QAAQ,CAACC,SAAS,CAACrB,aAAa,CAACG,KAAK,CAAC,GAAG,IAAI,CAACkB,SAAS,CAACU,QAAQ,CAAC;IACvE,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACEI,OAAOA,CAACJ,QAAiE,EAAE;IACzE,IAAI,CAACX,QAAQ,CAACe,OAAO,GAAGJ,QAAQ;IAChC,IAAI,CAACX,QAAQ,CAACC,SAAS,CAACrB,aAAa,CAACI,KAAK,CAAC,GAAG,IAAI,CAACiB,SAAS,CAACU,QAAQ,CAAC;IACvE,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEK,KAAKA,CACHL,QAGS,EACT;IACA,IAAI,CAACX,QAAQ,CAACgB,KAAK,GAAGL,QAAQ;IAC9B;IACA,IAAI,CAACX,QAAQ,CAACC,SAAS,CAACrB,aAAa,CAACO,GAAG,CAAC,GAAG,IAAI,CAACc,SAAS,CAACU,QAAQ,CAAC;IACrE,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACEM,UAAUA,CACRN,QAGS,EACT;IACA,IAAI,CAACX,QAAQ,CAACiB,UAAU,GAAGN,QAAQ;IACnC;IACA,IAAI,CAACX,QAAQ,CAACC,SAAS,CAACrB,aAAa,CAACQ,QAAQ,CAAC,GAAG,IAAI,CAACa,SAAS,CAACU,QAAQ,CAAC;IAC1E,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACEO,aAAaA,CAACP,QAA+B,EAAE;IAC7C,IAAI,CAACZ,MAAM,CAACoB,gBAAgB,GAAG,IAAI;IACnC,IAAI,CAACnB,QAAQ,CAACkB,aAAa,GAAGP,QAAQ;IACtC,IAAI,CAACX,QAAQ,CAACC,SAAS,CAACrB,aAAa,CAACS,YAAY,CAAC,GACjD,IAAI,CAACY,SAAS,CAACU,QAAQ,CAAC;IAE1B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACES,aAAaA,CAACT,QAA+B,EAAE;IAC7C,IAAI,CAACZ,MAAM,CAACoB,gBAAgB,GAAG,IAAI;IACnC,IAAI,CAACnB,QAAQ,CAACoB,aAAa,GAAGT,QAAQ;IACtC,IAAI,CAACX,QAAQ,CAACC,SAAS,CAACrB,aAAa,CAACU,YAAY,CAAC,GACjD,IAAI,CAACW,SAAS,CAACU,QAAQ,CAAC;IAE1B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACEU,WAAWA,CAACV,QAA+B,EAAE;IAC3C,IAAI,CAACZ,MAAM,CAACoB,gBAAgB,GAAG,IAAI;IACnC,IAAI,CAACnB,QAAQ,CAACqB,WAAW,GAAGV,QAAQ;IACpC,IAAI,CAACX,QAAQ,CAACC,SAAS,CAACrB,aAAa,CAACW,UAAU,CAAC,GAC/C,IAAI,CAACU,SAAS,CAACU,QAAQ,CAAC;IAE1B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACEW,kBAAkBA,CAACX,QAA+B,EAAE;IAClD,IAAI,CAACZ,MAAM,CAACoB,gBAAgB,GAAG,IAAI;IACnC,IAAI,CAACnB,QAAQ,CAACsB,kBAAkB,GAAGX,QAAQ;IAC3C,IAAI,CAACX,QAAQ,CAACC,SAAS,CAACrB,aAAa,CAACY,iBAAiB,CAAC,GACtD,IAAI,CAACS,SAAS,CAACU,QAAQ,CAAC;IAE1B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEY,OAAOA,CAACA,OAAgB,EAAE;IACxB,IAAI,CAACxB,MAAM,CAACwB,OAAO,GAAGA,OAAO;IAC7B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEC,uBAAuBA,CAAClB,KAAc,EAAE;IACtC,IAAI,CAACP,MAAM,CAACyB,uBAAuB,GAAGlB,KAAK;IAC3C,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEmB,OAAOA,CAACA,OAAgB,EAAE;IACxB,IAAI,CAAC1B,MAAM,CAAC0B,OAAO,GAAGA,OAAO;IAC7B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEC,YAAYA,CAACA,YAA0B,EAAE;IACvC,IAAI,CAAC3B,MAAM,CAAC2B,YAAY,GAAGA,YAAY;IACvC,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACA,WAAwB,EAAE;IACpC,IAAI,CAAC5B,MAAM,CAAC4B,WAAW,GAAGA,WAAW;IACrC,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEC,OAAOA,CAACA,OAAgB,EAAE;IACxB,IAAI,CAAC7B,MAAM,CAAC6B,OAAO,GAAGA,OAAO;IAC7B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEC,+BAA+BA,CAAC,GAAGC,QAAuC,EAAE;IAC1E,KAAK,MAAMzB,OAAO,IAAIyB,QAAQ,EAAE;MAC9B,IAAI,CAAC3B,aAAa,CAAC,kBAAkB,EAAEE,OAAO,CAAC;IACjD;IACA,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACE0B,4BAA4BA,CAAC,GAAGD,QAAuC,EAAE;IACvE,KAAK,MAAMzB,OAAO,IAAIyB,QAAQ,EAAE;MAC9B,IAAI,CAAC3B,aAAa,CAAC,eAAe,EAAEE,OAAO,CAAC;IAC9C;IACA,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACE2B,qBAAqBA,CAAC,GAAGF,QAAuC,EAAE;IAChE,KAAK,MAAMzB,OAAO,IAAIyB,QAAQ,EAAE;MAC9B,IAAI,CAAC3B,aAAa,CAAC,gBAAgB,EAAEE,OAAO,CAAC;IAC/C;IACA,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACE4B,UAAUA,CAACC,EAAU,EAAE;IACrB,IAAI,CAACnC,MAAM,CAACoC,MAAM,GAAGD,EAAE;IACvB,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEE,oBAAoBA,CAAC9B,KAAc,EAAE;IACnC,IAAI,CAACP,MAAM,CAACqC,oBAAoB,GAAG9B,KAAK;IACxC,OAAO,IAAI;EACb;EAEA+B,UAAUA,CAAA,EAAG;IACX,IAAI,CAACxC,UAAU,GAAG,IAAAyC,oCAAiB,EAAC,CAAC;IAErC,IAAI,CAACtC,QAAQ,GAAG;MAAE,GAAG,IAAI,CAACA,QAAQ;MAAEH,UAAU,EAAE,IAAI,CAACA;IAAW,CAAC;IAEjE,IAAI,IAAI,CAACE,MAAM,CAACW,GAAG,EAAE;MACnB,IAAI,CAACX,MAAM,CAACW,GAAG,CAAC6B,OAAO,GAAG,IAAmB;IAC/C;EACF;EAEAC,cAAcA,CAAA,EAAkB;IAC9B,OAAO,CAAC,IAAI,CAAgB;EAC9B;;EAEA;EACAC,OAAOA,CAAA,EAAG,CAAC;EAEX,IAAIC,mBAAmBA,CAAA,EAAY;IACjC;IACA;IACA;IACA,OACE,IAAI,CAAC3C,MAAM,CAAC6B,OAAO,KAAK,IAAI,IAC5B,CAAC,IAAI,CAAC5B,QAAQ,CAACC,SAAS,CAAC0C,QAAQ,CAAC,KAAK,CAAC,IACxC,CAAC,IAAAC,+BAAwB,EAAC,CAAC;EAE/B;AACF;AAAC/D,OAAA,CAAAc,WAAA,GAAAA,WAAA;AAEM,MAAekD,oBAAoB,SAGhClD,WAAW,CAAgB;EACnC;AACF;AACA;AACA;EACEmD,QAAQA,CAACnC,QAA4D,EAAE;IACrE,IAAI,CAACX,QAAQ,CAAC8C,QAAQ,GAAGnC,QAAQ;IACjC,IAAI,CAACX,QAAQ,CAACC,SAAS,CAACrB,aAAa,CAACK,MAAM,CAAC,GAAG,IAAI,CAACgB,SAAS,CAACU,QAAQ,CAAC;IACxE,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEoC,QAAQA,CACNpC,QAES,EACT;IACA,IAAI,CAACX,QAAQ,CAAC+C,QAAQ,GAAGpC,QAAQ;IACjC,IAAI,CAACX,QAAQ,CAACC,SAAS,CAACrB,aAAa,CAACM,MAAM,CAAC,GAAG,IAAI,CAACe,SAAS,CAACU,QAAQ,CAAC;IACxE,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEqC,gBAAgBA,CAACA,gBAAyB,EAAE;IAC1C,IAAI,CAACjD,MAAM,CAACiD,gBAAgB,GAAGA,gBAAgB;IAC/C,OAAO,IAAI;EACb;AACF;AAACnE,OAAA,CAAAgE,oBAAA,GAAAA,oBAAA", "ignoreList": []}