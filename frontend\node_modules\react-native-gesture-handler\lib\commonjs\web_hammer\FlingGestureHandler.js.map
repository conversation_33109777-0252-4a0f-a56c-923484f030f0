{"version": 3, "names": ["_hammerjs", "_interopRequireDefault", "require", "_constants", "_Errors", "_DraggingGestureHandler", "_utils", "e", "__esModule", "default", "FlingGestureHandler", "DraggingGestureHandler", "name", "NativeGestureClass", "Hammer", "Swipe", "onGestureActivated", "event", "sendEvent", "eventType", "INPUT_MOVE", "isFinal", "<PERSON><PERSON><PERSON><PERSON>", "isGestureRunning", "hasGestureFailed", "INPUT_END", "onRawEvent", "ev", "setTimeout", "cancelEvent", "gesture", "hammer", "get", "options", "enable", "onStart", "getHammerConfig", "pointers", "config", "numberOfPointers", "direction", "getDirection", "getTargetDirections", "directions", "Direction", "RIGHT", "push", "DIRECTION_RIGHT", "LEFT", "DIRECTION_LEFT", "UP", "DIRECTION_UP", "DOWN", "DIRECTION_DOWN", "getConfig", "DIRECTION_HORIZONTAL", "DIRECTION_VERTICAL", "Set", "length", "DIRECTION_NONE", "DIRECTION_ALL", "isGestureEnabledForEvent", "_recognizer", "maxPointers", "pointer<PERSON><PERSON><PERSON>", "validPointerCount", "failed", "success", "updateGestureConfig", "props", "isnan", "GesturePropError", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["web_hammer/FlingGestureHandler.ts"], "mappings": ";;;;;;AAEA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,UAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,uBAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAAgC,SAAAD,uBAAAM,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAPhC;AACA;;AASA,MAAMG,mBAAmB,SAASC,+BAAsB,CAAC;EACvD,IAAIC,IAAIA,CAAA,EAAG;IACT,OAAO,OAAO;EAChB;EAEA,IAAIC,kBAAkBA,CAAA,EAAG;IACvB,OAAOC,iBAAM,CAACC,KAAK;EACrB;EAEAC,kBAAkBA,CAACC,KAAqB,EAAE;IACxC,IAAI,CAACC,SAAS,CAAC;MACb,GAAGD,KAAK;MACRE,SAAS,EAAEL,iBAAM,CAACM,UAAU;MAC5BC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACX,CAAC,CAAC;IACF,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACN,SAAS,CAAC;MACb,GAAGD,KAAK;MACRE,SAAS,EAAEL,iBAAM,CAACW,SAAS;MAC3BJ,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;EAEAK,UAAUA,CAACC,EAAkB,EAAE;IAC7B,KAAK,CAACD,UAAU,CAACC,EAAE,CAAC;IACpB,IAAI,IAAI,CAACH,gBAAgB,EAAE;MACzB;IACF;IACA;IACA;IACA,IAAIG,EAAE,CAACN,OAAO,EAAE;MACdO,UAAU,CAAC,MAAM;QACf,IAAI,IAAI,CAACL,gBAAgB,EAAE;UACzB,IAAI,CAACM,WAAW,CAACF,EAAE,CAAC;QACtB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,CAAC,IAAI,CAACH,gBAAgB,IAAI,CAAC,IAAI,CAACD,gBAAgB,EAAE;MAC3D;MACA,MAAMO,OAAO,GAAG,IAAI,CAACC,MAAM,CAAEC,GAAG,CAAC,IAAI,CAACpB,IAAI,CAAC;MAC3C;MACA,IAAIkB,OAAO,CAACG,OAAO,CAACC,MAAM,CAACJ,OAAO,EAAEH,EAAE,CAAC,EAAE;QACvC,IAAI,CAACQ,OAAO,CAACR,EAAE,CAAC;QAChB,IAAI,CAACT,SAAS,CAACS,EAAE,CAAC;MACpB;IACF;EACF;EAEAS,eAAeA,CAAA,EAAG;IAChB,OAAO;MACL;MACAC,QAAQ,EAAE,IAAI,CAACC,MAAM,CAACC,gBAAgB;MACtCC,SAAS,EAAE,IAAI,CAACC,YAAY,CAAC;IAC/B,CAAC;EACH;EAEAC,mBAAmBA,CAACF,SAAiB,EAAE;IACrC,MAAMG,UAAU,GAAG,EAAE;IACrB,IAAIH,SAAS,GAAGI,oBAAS,CAACC,KAAK,EAAE;MAC/BF,UAAU,CAACG,IAAI,CAAChC,iBAAM,CAACiC,eAAe,CAAC;IACzC;IACA,IAAIP,SAAS,GAAGI,oBAAS,CAACI,IAAI,EAAE;MAC9BL,UAAU,CAACG,IAAI,CAAChC,iBAAM,CAACmC,cAAc,CAAC;IACxC;IACA,IAAIT,SAAS,GAAGI,oBAAS,CAACM,EAAE,EAAE;MAC5BP,UAAU,CAACG,IAAI,CAAChC,iBAAM,CAACqC,YAAY,CAAC;IACtC;IACA,IAAIX,SAAS,GAAGI,oBAAS,CAACQ,IAAI,EAAE;MAC9BT,UAAU,CAACG,IAAI,CAAChC,iBAAM,CAACuC,cAAc,CAAC;IACxC;IACA;IACA,OAAOV,UAAU;EACnB;EAEAF,YAAYA,CAAA,EAAG;IACb;IACA,MAAM;MAAED;IAAU,CAAC,GAAG,IAAI,CAACc,SAAS,CAAC,CAAC;IAEtC,IAAIX,UAAU,GAAG,EAAE;IACnB,IAAIH,SAAS,GAAGI,oBAAS,CAACC,KAAK,EAAE;MAC/BF,UAAU,CAACG,IAAI,CAAChC,iBAAM,CAACyC,oBAAoB,CAAC;IAC9C;IACA,IAAIf,SAAS,GAAGI,oBAAS,CAACI,IAAI,EAAE;MAC9BL,UAAU,CAACG,IAAI,CAAChC,iBAAM,CAACyC,oBAAoB,CAAC;IAC9C;IACA,IAAIf,SAAS,GAAGI,oBAAS,CAACM,EAAE,EAAE;MAC5BP,UAAU,CAACG,IAAI,CAAChC,iBAAM,CAAC0C,kBAAkB,CAAC;IAC5C;IACA,IAAIhB,SAAS,GAAGI,oBAAS,CAACQ,IAAI,EAAE;MAC9BT,UAAU,CAACG,IAAI,CAAChC,iBAAM,CAAC0C,kBAAkB,CAAC;IAC5C;IACAb,UAAU,GAAG,CAAC,GAAG,IAAIc,GAAG,CAACd,UAAU,CAAC,CAAC;IAErC,IAAIA,UAAU,CAACe,MAAM,KAAK,CAAC,EAAE,OAAO5C,iBAAM,CAAC6C,cAAc;IACzD,IAAIhB,UAAU,CAACe,MAAM,KAAK,CAAC,EAAE,OAAOf,UAAU,CAAC,CAAC,CAAC;IACjD,OAAO7B,iBAAM,CAAC8C,aAAa;EAC7B;EAEAC,wBAAwBA,CACtB;IAAEtB;EAAsB,CAAC,EACzBuB,WAAgB,EAChB;IAAEC,WAAW,EAAEC;EAAmB,CAAC,EACnC;IACA,MAAMC,iBAAiB,GAAGD,aAAa,KAAKzB,gBAAgB;IAC5D,IAAI,CAAC0B,iBAAiB,IAAI,IAAI,CAAC1C,gBAAgB,EAAE;MAC/C,OAAO;QAAE2C,MAAM,EAAE;MAAK,CAAC;IACzB;IACA,OAAO;MAAEC,OAAO,EAAEF;IAAkB,CAAC;EACvC;EAEAG,mBAAmBA,CAAC;IAAE7B,gBAAgB,GAAG,CAAC;IAAEC,SAAS;IAAE,GAAG6B;EAAW,CAAC,EAAE;IACtE,IAAI,IAAAC,YAAK,EAAC9B,SAAS,CAAC,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;MACrD,MAAM,IAAI+B,wBAAgB,CAAC,WAAW,EAAE/B,SAAS,EAAE,QAAQ,CAAC;IAC9D;IACA,OAAO,KAAK,CAAC4B,mBAAmB,CAAC;MAC/B7B,gBAAgB;MAChBC,SAAS;MACT,GAAG6B;IACL,CAAC,CAAC;EACJ;AACF;AAAC,IAAAG,QAAA,GAAAC,OAAA,CAAAhE,OAAA,GAEcC,mBAAmB", "ignoreList": []}