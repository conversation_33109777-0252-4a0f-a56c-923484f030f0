{"version": 3, "names": ["FlatList", "isRNSVGElement", "findNodeHandle", "viewRef", "_listRef", "_scrollRef", "<PERSON><PERSON><PERSON><PERSON>", "viewTag", "undefined", "Element", "style", "display", "elementRef", "current", "element"], "sourceRoot": "../../src", "sources": ["findNodeHandle.web.ts"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,cAAc;AAEvC,SAASC,cAAc,QAAQ,aAAa;AAE5C,eAAe,SAASC,cAAcA,CACpCC,OAA8D,EAC3B;EACnC;EACA,IAAIA,OAAO,YAAYH,QAAQ,EAAE;IAC/B;IACA,OAAOG,OAAO,CAACC,QAAQ,CAACC,UAAU,CAACC,UAAU;EAC/C;EACA;EACA;EACA;EACA,IAAKH,OAAO,EAAwBI,OAAO,KAAKC,SAAS,EAAE;IACzD,OAAON,cAAc,CAAEC,OAAO,CAAuBI,OAAO,CAAC;EAC/D;EAEA,IAAIJ,OAAO,YAAYM,OAAO,EAAE;IAC9B,IAAIN,OAAO,CAACO,KAAK,CAACC,OAAO,KAAK,UAAU,EAAE;MACxC,OAAOT,cAAc,CAACC,OAAO,CAACG,UAAyB,CAAC;IAC1D;IAEA,OAAOH,OAAO;EAChB;EAEA,IAAIF,cAAc,CAACE,OAAO,CAAC,EAAE;IAC3B,OAAQA,OAAO,CAAYS,UAAU,CAACC,OAAO;EAC/C;;EAEA;EACA;EACA,IAAIC,OAAO,GAAIX,OAAO,EAAwBU,OAAO;EAErD,OAAOC,OAAO,IAAIA,OAAO,CAACJ,KAAK,CAACC,OAAO,KAAK,UAAU,EAAE;IACtDG,OAAO,GAAGA,OAAO,CAACR,UAAyB;EAC7C;EAEA,OAAOQ,OAAO;AAChB", "ignoreList": []}