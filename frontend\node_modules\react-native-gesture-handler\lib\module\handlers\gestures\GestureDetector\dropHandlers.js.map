{"version": 3, "names": ["unregister<PERSON><PERSON><PERSON>", "RNGestureHandlerModule", "scheduleFlushOperations", "MountRegistry", "dropHandlers", "preparedGesture", "handler", "attachedGestures", "dropGestureHandler", "handlerTag", "config", "testId", "gestureWillUnmount"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/dropHandlers.ts"], "mappings": ";;AAAA,SAASA,iBAAiB,QAAQ,wBAAwB;AAC1D,OAAOC,sBAAsB,MAAM,iCAAiC;AACpE,SAASC,uBAAuB,QAAQ,aAAa;AAErD,SAASC,aAAa,QAAQ,wBAAwB;AAEtD,OAAO,SAASC,YAAYA,CAACC,eAAqC,EAAE;EAClE,KAAK,MAAMC,OAAO,IAAID,eAAe,CAACE,gBAAgB,EAAE;IACtDN,sBAAsB,CAACO,kBAAkB,CAACF,OAAO,CAACG,UAAU,CAAC;IAE7DT,iBAAiB,CAACM,OAAO,CAACG,UAAU,EAAEH,OAAO,CAACI,MAAM,CAACC,MAAM,CAAC;IAE5DR,aAAa,CAACS,kBAAkB,CAACN,OAAO,CAAC;EAC3C;EAEAJ,uBAAuB,CAAC,CAAC;AAC3B", "ignoreList": []}