{"version": 3, "names": ["NodeManager", "GestureStateManager", "create", "handlerTag", "begin", "<PERSON><PERSON><PERSON><PERSON>", "activate", "fail", "end"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/gestureStateManager.web.ts"], "mappings": ";;AAAA,OAAOA,WAAW,MAAM,6BAA6B;AAGrD,OAAO,MAAMC,mBAAmB,GAAG;EACjCC,MAAMA,CAACC,UAAkB,EAA2B;IAClD,OAAO;MACLC,KAAK,EAAEA,CAAA,KAAM;QACXJ,WAAW,CAACK,UAAU,CAACF,UAAU,CAAC,CAACC,KAAK,CAAC,CAAC;MAC5C,CAAC;MAEDE,QAAQ,EAAEA,CAAA,KAAM;QACdN,WAAW,CAACK,UAAU,CAACF,UAAU,CAAC,CAACG,QAAQ,CAAC,IAAI,CAAC;MACnD,CAAC;MAEDC,IAAI,EAAEA,CAAA,KAAM;QACVP,WAAW,CAACK,UAAU,CAACF,UAAU,CAAC,CAACI,IAAI,CAAC,CAAC;MAC3C,CAAC;MAEDC,GAAG,EAAEA,CAAA,KAAM;QACTR,WAAW,CAACK,UAAU,CAACF,UAAU,CAAC,CAACK,GAAG,CAAC,CAAC;MAC1C;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}