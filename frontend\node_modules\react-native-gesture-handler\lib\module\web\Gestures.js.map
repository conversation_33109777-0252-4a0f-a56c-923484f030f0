{"version": 3, "names": ["PanGestureHandler", "TapGestureHandler", "LongPressGestureHandler", "PinchGestureHandler", "RotationGestureHandler", "FlingGestureHandler", "NativeViewGestureHandler", "ManualGestureHandler", "HoverGestureHandler", "HammerNativeViewGestureHandler", "HammerPanGestureHandler", "HammerTapGestureHandler", "HammerLongPressGestureHandler", "HammerPinchGestureHandler", "HammerRotationGestureHandler", "HammerFlingGestureHandler", "Gestures", "HammerGestures"], "sourceRoot": "../../../src", "sources": ["web/Gestures.ts"], "mappings": ";;AAAA;AACA,OAAOA,iBAAiB,MAAM,8BAA8B;AAC5D,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,OAAOC,uBAAuB,MAAM,oCAAoC;AACxE,OAAOC,mBAAmB,MAAM,gCAAgC;AAChE,OAAOC,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,mBAAmB,MAAM,gCAAgC;AAChE,OAAOC,wBAAwB,MAAM,qCAAqC;AAC1E,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,mBAAmB,MAAM,gCAAgC;;AAEhE;AACA,OAAOC,8BAA8B,MAAM,wCAAwC;AACnF,OAAOC,uBAAuB,MAAM,iCAAiC;AACrE,OAAOC,uBAAuB,MAAM,iCAAiC;AACrE,OAAOC,6BAA6B,MAAM,uCAAuC;AACjF,OAAOC,yBAAyB,MAAM,mCAAmC;AACzE,OAAOC,4BAA4B,MAAM,sCAAsC;AAC/E,OAAOC,yBAAyB,MAAM,mCAAmC;AAEzE,OAAO,MAAMC,QAAQ,GAAG;EACtBV,wBAAwB;EACxBN,iBAAiB;EACjBC,iBAAiB;EACjBC,uBAAuB;EACvBC,mBAAmB;EACnBC,sBAAsB;EACtBC,mBAAmB;EACnBE,oBAAoB;EACpBC;AACF,CAAC;AAED,OAAO,MAAMS,cAAc,GAAG;EAC5BX,wBAAwB,EAAEG,8BAA8B;EACxDT,iBAAiB,EAAEU,uBAAuB;EAC1CT,iBAAiB,EAAEU,uBAAuB;EAC1CT,uBAAuB,EAAEU,6BAA6B;EACtDT,mBAAmB,EAAEU,yBAAyB;EAC9CT,sBAAsB,EAAEU,4BAA4B;EACpDT,mBAAmB,EAAEU;AACvB,CAAC", "ignoreList": []}