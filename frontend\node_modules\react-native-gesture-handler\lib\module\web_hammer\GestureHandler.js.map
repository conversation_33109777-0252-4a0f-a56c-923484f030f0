{"version": 3, "names": ["Hammer", "findNodeHandle", "State", "EventMap", "NodeManager", "ghQueueMicrotask", "gestureInstances", "Gesture<PERSON>andler", "isGestureRunning", "view", "hasGestureFailed", "hammer", "initialRotation", "config", "previousState", "UNDETERMINED", "pendingGestures", "oldState", "lastSentState", "id", "name", "gestureInstance", "isNative", "isDiscrete", "shouldEnableGestureOnSetup", "Error", "constructor", "hasCustomActivationCriteria", "getConfig", "onWaitingEnded", "_gesture", "removePendingGesture", "addPendingGesture", "gesture", "isGestureEnabledForEvent", "_config", "_recognizer", "_event", "success", "NativeGestureClass", "updateHasCustomActivationCriteria", "clearSelfAsPending", "Array", "isArray", "waitFor", "updateGestureConfig", "enabled", "props", "ensureConfig", "sync", "destroy", "stop", "isPointInView", "x", "y", "rect", "getBoundingClientRect", "pointerInside", "left", "right", "top", "bottom", "getState", "type", "transformEventData", "event", "eventType", "maxPointers", "numberOfPointers", "changedTouch", "changedPointers", "clientX", "clientY", "state", "nativeEvent", "transformNativeEvent", "handlerTag", "target", "ref", "undefined", "timeStamp", "Date", "now", "sendEvent", "onGestureHandlerEvent", "onGestureHandlerStateChange", "propsRef", "current", "invokeNullableMethod", "cancelPendingGestures", "Object", "values", "cancelEvent", "notifyPendingGestures", "onGestureEnded", "forceInvalidate", "INPUT_CANCEL", "isFinal", "onRawEvent", "<PERSON><PERSON><PERSON><PERSON>", "shouldUseTouchEvents", "simultaneousHandlers", "some", "handler", "<PERSON><PERSON><PERSON><PERSON>", "SUPPORTS_TOUCH", "window", "Manager", "inputClass", "TouchInput", "getHammerConfig", "add", "on", "ev", "rotation", "setTimeout", "setupEvents", "onStart", "onGestureActivated", "deltaX", "deltaY", "__initialX", "__initialY", "onSuccess", "_getPendingGestures", "length", "stillWaiting", "filter", "pointers", "minPointers", "get", "enable", "recognizer", "inputData", "options", "_stillWaiting", "deltaRotation", "failed", "simulateCancelEvent", "params", "set", "_inputData", "minDist", "minDistSq", "minVelocity", "minVelocitySq", "maxDist", "maxDistSq", "asArray", "map", "<PERSON><PERSON><PERSON><PERSON>", "v", "configProps", "for<PERSON>ach", "prop", "Number", "NaN", "method", "__<PERSON><PERSON><PERSON><PERSON>", "arg<PERSON><PERSON><PERSON>", "__nodeConfig", "index", "key", "value", "entries", "nativeValue", "setValue"], "sourceRoot": "../../../src", "sources": ["web_hammer/GestureHandler.ts"], "mappings": ";;AAAA;AACA;AACA,OAAOA,MAAM,MAAM,gBAAgB;AACnC,SAASC,cAAc,QAAQ,cAAc;AAE7C,SAASC,KAAK,QAAQ,UAAU;AAChC,SAASC,QAAQ,QAAQ,aAAa;AACtC,OAAO,KAAKC,WAAW,MAAM,eAAe;AAC5C,SAASC,gBAAgB,QAAQ,qBAAqB;;AAEtD;;AA2BA,IAAIC,gBAAgB,GAAG,CAAC;AAExB,MAAeC,cAAc,CAAC;EAErBC,gBAAgB,GAAG,KAAK;EACxBC,IAAI,GAAkB,IAAI;EAEvBC,gBAAgB,GAAG,KAAK;EACxBC,MAAM,GAAyB,IAAI;EACnCC,eAAe,GAAkB,IAAI;EAGrCC,MAAM,GAAW,CAAC,CAAC;EACnBC,aAAa,GAAUZ,KAAK,CAACa,YAAY;EAC3CC,eAAe,GAAyB,CAAC,CAAC;EAC1CC,QAAQ,GAAUf,KAAK,CAACa,YAAY;EACpCG,aAAa,GAAiB,IAAI;EAQ1C,IAAIC,EAAEA,CAAA,EAAG;IACP,OAAO,GAAG,IAAI,CAACC,IAAI,GAAG,IAAI,CAACC,eAAe,EAAE;EAC9C;;EAEA;EACA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACb,OAAO,KAAK;EACd;EAEA,IAAIC,UAAUA,CAAA,EAAG;IACf,OAAO,KAAK;EACd;EAEA,IAAIC,0BAA0BA,CAAA,EAAY;IACxC,MAAM,IAAIC,KAAK,CAAC,yDAAyD,CAAC;EAC5E;EAEAC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACL,eAAe,GAAGf,gBAAgB,EAAE;IACzC,IAAI,CAACqB,2BAA2B,GAAG,KAAK;EAC1C;EAEAC,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAACf,MAAM;EACpB;EAEAgB,cAAcA,CAACC,QAAc,EAAE,CAAC;EAEhCC,oBAAoBA,CAACZ,EAAU,EAAE;IAC/B,OAAO,IAAI,CAACH,eAAe,CAACG,EAAE,CAAC;EACjC;EAEAa,iBAAiBA,CAACC,OAAa,EAAE;IAC/B,IAAI,CAACjB,eAAe,CAACiB,OAAO,CAACd,EAAE,CAAC,GAAGc,OAAO;EAC5C;EAEAC,wBAAwBA,CACtBC,OAAY,EACZC,WAAgB,EAChBC,MAAW,EAC8B;IACzC,OAAO;MAAEC,OAAO,EAAE;IAAK,CAAC;EAC1B;EAEA,IAAIC,kBAAkBA,CAAA,EAAqB;IACzC,MAAM,IAAId,KAAK,CAAC,iDAAiD,CAAC;EACpE;EAEAe,iCAAiCA,CAACL,OAAe,EAAE;IACjD,OAAO,IAAI;EACb;EAEAM,kBAAkB,GAAGA,CAAA,KAAM;IACzB,IAAIC,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC9B,MAAM,CAAC+B,OAAO,CAAC,EAAE;MACtC,KAAK,MAAMX,OAAO,IAAI,IAAI,CAACpB,MAAM,CAAC+B,OAAO,EAAE;QACzCX,OAAO,CAACF,oBAAoB,CAAC,IAAI,CAACZ,EAAE,CAAC;MACvC;IACF;EACF,CAAC;EAED0B,mBAAmBA,CAAC;IAAEC,OAAO,GAAG,IAAI;IAAE,GAAGC;EAAM,CAAC,EAAE;IAChD,IAAI,CAACN,kBAAkB,CAAC,CAAC;IAEzB,IAAI,CAAC5B,MAAM,GAAG,IAAI,CAACmC,YAAY,CAAC;MAAEF,OAAO;MAAE,GAAGC;IAAM,CAAC,CAAC;IACtD,IAAI,CAACpB,2BAA2B,GAAG,IAAI,CAACa,iCAAiC,CACvE,IAAI,CAAC3B,MACP,CAAC;IACD,IAAI6B,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC9B,MAAM,CAAC+B,OAAO,CAAC,EAAE;MACtC,KAAK,MAAMX,OAAO,IAAI,IAAI,CAACpB,MAAM,CAAC+B,OAAO,EAAE;QACzCX,OAAO,CAACD,iBAAiB,CAAC,IAAI,CAAC;MACjC;IACF;IAEA,IAAI,IAAI,CAACrB,MAAM,EAAE;MACf,IAAI,CAACsC,IAAI,CAAC,CAAC;IACb;IACA,OAAO,IAAI,CAACpC,MAAM;EACpB;EAEAqC,OAAO,GAAGA,CAAA,KAAM;IACd,IAAI,CAACT,kBAAkB,CAAC,CAAC;IAEzB,IAAI,IAAI,CAAC9B,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACwC,IAAI,CAAC,KAAK,CAAC;MACvB,IAAI,CAACxC,MAAM,CAACuC,OAAO,CAAC,CAAC;IACvB;IACA,IAAI,CAACvC,MAAM,GAAG,IAAI;EACpB,CAAC;EAEDyC,aAAa,GAAGA,CAAC;IAAEC,CAAC;IAAEC;EAA4B,CAAC,KAAK;IACtD;IACA,MAAMC,IAAI,GAAG,IAAI,CAAC9C,IAAI,CAAE+C,qBAAqB,CAAC,CAAC;IAC/C,MAAMC,aAAa,GACjBJ,CAAC,IAAIE,IAAI,CAACG,IAAI,IAAIL,CAAC,IAAIE,IAAI,CAACI,KAAK,IAAIL,CAAC,IAAIC,IAAI,CAACK,GAAG,IAAIN,CAAC,IAAIC,IAAI,CAACM,MAAM;IACxE,OAAOJ,aAAa;EACtB,CAAC;EAEDK,QAAQA,CAACC,IAA2B,EAAS;IAC3C;IACA,IAAIA,IAAI,IAAI,CAAC,EAAE;MACb,OAAO,CAAC;IACV;IACA,OAAO5D,QAAQ,CAAC4D,IAAI,CAAC;EACvB;EAEAC,kBAAkBA,CAACC,KAAqB,EAAE;IACxC,MAAM;MAAEC,SAAS;MAAEC,WAAW,EAAEC;IAAiB,CAAC,GAAGH,KAAK;IAC1D;IACA,MAAMI,YAAY,GAAGJ,KAAK,CAACK,eAAe,CAAC,CAAC,CAAC;IAC7C,MAAMb,aAAa,GAAG,IAAI,CAACL,aAAa,CAAC;MACvCC,CAAC,EAAEgB,YAAY,CAACE,OAAO;MACvBjB,CAAC,EAAEe,YAAY,CAACG;IAClB,CAAC,CAAC;;IAEF;IACA,MAAMC,KAAK,GAAG,IAAI,CAACX,QAAQ,CAACI,SAA0B,CAAC;IACvD,IAAIO,KAAK,KAAK,IAAI,CAAC3D,aAAa,EAAE;MAChC,IAAI,CAACG,QAAQ,GAAG,IAAI,CAACH,aAAa;MAClC,IAAI,CAACA,aAAa,GAAG2D,KAAK;IAC5B;IAEA,OAAO;MACLC,WAAW,EAAE;QACXN,gBAAgB;QAChBK,KAAK;QACLhB,aAAa;QACb,GAAG,IAAI,CAACkB,oBAAoB,CAACV,KAAK,CAAC;QACnC;QACAW,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BC,MAAM,EAAE,IAAI,CAACC,GAAG;QAChB;QACA;QACA;QACA7D,QAAQ,EACNwD,KAAK,KAAK,IAAI,CAAC3D,aAAa,IAAI2D,KAAK,IAAI,CAAC,GACtC,IAAI,CAACxD,QAAQ,GACb8D;MACR,CAAC;MACDC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;EACH;EAEAP,oBAAoBA,CAACtC,MAAsB,EAAE;IAC3C,OAAO,CAAC,CAAC;EACX;EAEA8C,SAAS,GAAIT,WAA2B,IAAK;IAC3C,MAAM;MAAEU,qBAAqB;MAAEC;IAA4B,CAAC,GAC1D,IAAI,CAACC,QAAQ,CAACC,OAAO;IAEvB,MAAMtB,KAAK,GAAG,IAAI,CAACD,kBAAkB,CAACU,WAAW,CAAC;IAElDc,oBAAoB,CAACJ,qBAAqB,EAAEnB,KAAK,CAAC;IAClD,IAAI,IAAI,CAAC/C,aAAa,KAAK+C,KAAK,CAACS,WAAW,CAACD,KAAK,EAAE;MAClD,IAAI,CAACvD,aAAa,GAAG+C,KAAK,CAACS,WAAW,CAACD,KAAc;MACrDe,oBAAoB,CAACH,2BAA2B,EAAEpB,KAAK,CAAC;IAC1D;EACF,CAAC;EAEDwB,qBAAqBA,CAACxB,KAAqB,EAAE;IAC3C,KAAK,MAAMhC,OAAO,IAAIyD,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC3E,eAAe,CAAC,EAAE;MACzD,IAAIiB,OAAO,IAAIA,OAAO,CAACzB,gBAAgB,EAAE;QACvCyB,OAAO,CAACvB,gBAAgB,GAAG,IAAI;QAC/BuB,OAAO,CAAC2D,WAAW,CAAC3B,KAAK,CAAC;MAC5B;IACF;EACF;EAEA4B,qBAAqBA,CAAA,EAAG;IACtB,KAAK,MAAM5D,OAAO,IAAIyD,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC3E,eAAe,CAAC,EAAE;MACzD,IAAIiB,OAAO,EAAE;QACXA,OAAO,CAACJ,cAAc,CAAC,IAAI,CAAC;MAC9B;IACF;EACF;;EAEA;EACAiE,cAAcA,CAAC7B,KAAqB,EAAE;IACpC,IAAI,CAACzD,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACiF,qBAAqB,CAACxB,KAAK,CAAC;EACnC;EAEA8B,eAAeA,CAAC9B,KAAqB,EAAE;IACrC,IAAI,IAAI,CAACzD,gBAAgB,EAAE;MACzB,IAAI,CAACE,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAACkF,WAAW,CAAC3B,KAAK,CAAC;IACzB;EACF;EAEA2B,WAAWA,CAAC3B,KAAqB,EAAE;IACjC,IAAI,CAAC4B,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACV,SAAS,CAAC;MACb,GAAGlB,KAAK;MACRC,SAAS,EAAElE,MAAM,CAACgG,YAAY;MAC9BC,OAAO,EAAE;IACX,CAAC,CAAC;IACF,IAAI,CAACH,cAAc,CAAC7B,KAAK,CAAC;EAC5B;EAEAiC,UAAUA,CAAC;IAAEC;EAAwB,CAAC,EAAE;IACtC,IAAIA,OAAO,EAAE;MACX,IAAI,CAACzF,gBAAgB,GAAG,KAAK;IAC/B;EACF;EAEA0F,oBAAoBA,CAACvF,MAAc,EAAE;IACnC,OACEA,MAAM,CAACwF,oBAAoB,EAAEC,IAAI,CAAEC,OAAO,IAAKA,OAAO,CAACjF,QAAQ,CAAC,IAAI,KAAK;EAE7E;EAEAkF,OAAOA,CAAC1B,GAA2C,EAAEQ,QAAa,EAAE;IAClE,IAAIR,GAAG,IAAI,IAAI,EAAE;MACf,IAAI,CAAC5B,OAAO,CAAC,CAAC;MACd,IAAI,CAACzC,IAAI,GAAG,IAAI;MAChB;IACF;;IAEA;IACA,MAAMgG,cAAc,GAAG,cAAc,IAAIC,MAAM;IAC/C,IAAI,CAACpB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACR,GAAG,GAAGA,GAAG;;IAEd;IACA,IAAI,CAACrE,IAAI,GAAGR,cAAc,CAAC6E,GAAG,CAAC;;IAE/B;IACA;IACA;IACA,IAAI,CAACnE,MAAM,GACT8F,cAAc,IAAI,IAAI,CAACL,oBAAoB,CAAC,IAAI,CAACvF,MAAM,CAAC,GACpD,IAAIb,MAAM,CAAC2G,OAAO,CAAC,IAAI,CAAClG,IAAI,EAAS;MACnCmG,UAAU,EAAE5G,MAAM,CAAC6G;IACrB,CAAC,CAAC,GACF,IAAI7G,MAAM,CAAC2G,OAAO,CAAC,IAAI,CAAClG,IAAW,CAAC;IAE1C,IAAI,CAACQ,QAAQ,GAAGf,KAAK,CAACa,YAAY;IAClC,IAAI,CAACD,aAAa,GAAGZ,KAAK,CAACa,YAAY;IACvC,IAAI,CAACG,aAAa,GAAG,IAAI;IAEzB,MAAM;MAAEqB;IAAmB,CAAC,GAAG,IAAI;IACnC;IACA,MAAMN,OAAO,GAAG,IAAIM,kBAAkB,CAAC,IAAI,CAACuE,eAAe,CAAC,CAAC,CAAC;IAC9D,IAAI,CAACnG,MAAM,CAACoG,GAAG,CAAC9E,OAAO,CAAC;IAExB,IAAI,CAACtB,MAAM,CAACqG,EAAE,CAAC,cAAc,EAAGC,EAAe,IAAK;MAClD,IAAI,CAAC,IAAI,CAACpG,MAAM,CAACiC,OAAO,EAAE;QACxB,IAAI,CAACpC,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACF,gBAAgB,GAAG,KAAK;QAC7B;MACF;MAEA,IAAI,CAAC0F,UAAU,CAACe,EAA+B,CAAC;;MAEhD;MACA;MACA,IAAI,IAAI,CAACrG,eAAe,KAAK,IAAI,IAAIqG,EAAE,CAACC,QAAQ,KAAK,CAAC,EAAE;QACtD,IAAI,CAACtG,eAAe,GAAGqG,EAAE,CAACC,QAAQ;MACpC;MACA,IAAID,EAAE,CAAChB,OAAO,EAAE;QACd;QACAkB,UAAU,CAAC,MAAM;UACf,IAAI,CAACvG,eAAe,GAAG,IAAI;UAC3B,IAAI,CAACF,gBAAgB,GAAG,KAAK;QAC/B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAI,CAAC0G,WAAW,CAAC,CAAC;IAClB,IAAI,CAACnE,IAAI,CAAC,CAAC;EACb;EAEAmE,WAAWA,CAAA,EAAG;IACZ;IACA,IAAI,CAAC,IAAI,CAAC7F,UAAU,EAAE;MACpB,IAAI,CAACZ,MAAM,CAAEqG,EAAE,CAAC,GAAG,IAAI,CAAC5F,IAAI,OAAO,EAAG6C,KAAkB,IACtD,IAAI,CAACoD,OAAO,CAACpD,KAAkC,CACjD,CAAC;MACD,IAAI,CAACtD,MAAM,CAAEqG,EAAE,CACb,GAAG,IAAI,CAAC5F,IAAI,OAAO,IAAI,CAACA,IAAI,QAAQ,EACnC6C,KAAkB,IAAK;QACtB,IAAI,CAAC6B,cAAc,CAAC7B,KAAkC,CAAC;MACzD,CACF,CAAC;IACH;IACA,IAAI,CAACtD,MAAM,CAAEqG,EAAE,CAAC,IAAI,CAAC5F,IAAI,EAAG6F,EAAe,IACzC,IAAI,CAACK,kBAAkB,CAACL,EAA+B,CACzD,CAAC,CAAC,CAAC;EACL;EAEAI,OAAOA,CAAC;IAAEE,MAAM;IAAEC,MAAM;IAAEN;EAAyB,CAAC,EAAE;IACpD;IACA,IAAI,CAACjG,QAAQ,GAAGf,KAAK,CAACa,YAAY;IAClC,IAAI,CAACD,aAAa,GAAGZ,KAAK,CAACa,YAAY;IACvC,IAAI,CAACG,aAAa,GAAG,IAAI;IAEzB,IAAI,CAACV,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACiH,UAAU,GAAGF,MAAM;IACxB,IAAI,CAACG,UAAU,GAAGF,MAAM;IACxB,IAAI,CAAC5G,eAAe,GAAGsG,QAAQ;EACjC;EAEAI,kBAAkBA,CAACL,EAAkB,EAAE;IACrC,IAAI,CAAC9B,SAAS,CAAC8B,EAAE,CAAC;EACpB;EAEAU,SAASA,CAAA,EAAG,CAAC;EAEbC,mBAAmBA,CAAA,EAAG;IACpB,IAAIlF,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC9B,MAAM,CAAC+B,OAAO,CAAC,IAAI,IAAI,CAAC/B,MAAM,CAAC+B,OAAO,CAACiF,MAAM,EAAE;MACpE;MACA;MACA,MAAMC,YAAY,GAAG,IAAI,CAACjH,MAAM,CAAC+B,OAAO,CAACmF,MAAM,CAC7C,CAAC;QAAErH;MAAiB,CAAC,KAAKA,gBAAgB,KAAK,KACjD,CAAC;MACD,OAAOoH,YAAY;IACrB;IACA,OAAO,EAAE;EACX;EAEAhB,eAAeA,CAAA,EAAG;IAChB,MAAMkB,QAAQ,GACZ,IAAI,CAACnH,MAAM,CAACoH,WAAW,KAAK,IAAI,CAACpH,MAAM,CAACsD,WAAW,GAC/C,IAAI,CAACtD,MAAM,CAACoH,WAAW,GACvB,CAAC;IACP,OAAO;MACLD;IACF,CAAC;EACH;EAEA/E,IAAI,GAAGA,CAAA,KAAM;IACX,MAAMhB,OAAO,GAAG,IAAI,CAACtB,MAAM,CAAEuH,GAAG,CAAC,IAAI,CAAC9G,IAAI,CAAC;IAC3C,IAAI,CAACa,OAAO,EAAE;IAEd,MAAMkG,MAAM,GAAGA,CAACC,UAAe,EAAEC,SAAc,KAAK;MAClD,IAAI,CAAC,IAAI,CAACxH,MAAM,CAACiC,OAAO,EAAE;QACxB,IAAI,CAACtC,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACE,gBAAgB,GAAG,KAAK;QAC7B,OAAO,KAAK;MACd;;MAEA;MACA,IACE,CAAC2H,SAAS,IACV,CAACD,UAAU,CAACE,OAAO,IACnB,OAAOD,SAAS,CAAClE,WAAW,KAAK,WAAW,EAC5C;QACA,OAAO,IAAI,CAAC3C,0BAA0B;MACxC;MAEA,IAAI,IAAI,CAACd,gBAAgB,EAAE;QACzB,OAAO,KAAK;MACd;MAEA,IAAI,CAAC,IAAI,CAACa,UAAU,EAAE;QACpB,IAAI,IAAI,CAACf,gBAAgB,EAAE;UACzB,OAAO,IAAI;QACb;QACA;QACA;QACA,IAAI,CAAC+H,aAAa,GAAG,IAAI,CAACX,mBAAmB,CAAC,CAAC;QAC/C;QACA,IAAI,IAAI,CAACW,aAAa,CAACV,MAAM,EAAE;UAC7B;UACA;UACA,KAAK,MAAM5F,OAAO,IAAI,IAAI,CAACsG,aAAa,EAAE;YACxC;YACA,IAAI,CAACtG,OAAO,CAACV,UAAU,IAAIU,OAAO,CAACzB,gBAAgB,EAAE;cACnD,IAAI,CAACE,gBAAgB,GAAG,IAAI;cAC5B,IAAI,CAACF,gBAAgB,GAAG,KAAK;cAC7B,OAAO,KAAK;YACd;UACF;UACA;UACA,OAAO,KAAK;QACd;MACF;;MAEA;MACA,IAAI,CAAC,IAAI,CAACmB,2BAA2B,EAAE;QACrC,OAAO,IAAI;MACb;MAEA,MAAM6G,aAAa,GACjB,IAAI,CAAC5H,eAAe,IAAI,IAAI,GACxB,CAAC,GACDyH,SAAS,CAACnB,QAAQ,GAAG,IAAI,CAACtG,eAAe;MAC/C;MACA,MAAM;QAAE0B,OAAO;QAAEmG;MAAO,CAAC,GAAG,IAAI,CAACvG,wBAAwB,CACvD,IAAI,CAACN,SAAS,CAAC,CAAC,EAChBwG,UAAU,EACV;QACE,GAAGC,SAAS;QACZG;MACF,CACF,CAAC;MAED,IAAIC,MAAM,EAAE;QACV,IAAI,CAACC,mBAAmB,CAACL,SAAS,CAAC;QACnC,IAAI,CAAC3H,gBAAgB,GAAG,IAAI;MAC9B;MACA,OAAO4B,OAAO;IAChB,CAAC;IAED,MAAMqG,MAAM,GAAG,IAAI,CAAC7B,eAAe,CAAC,CAAC;IACrC;IACA7E,OAAO,CAAC2G,GAAG,CAAC;MAAE,GAAGD,MAAM;MAAER;IAAO,CAAC,CAAC;EACpC,CAAC;EAEDO,mBAAmBA,CAACG,UAAe,EAAE,CAAC;;EAEtC;EACA7F,YAAYA,CAACnC,MAAc,EAAoB;IAC7C,MAAMkC,KAAK,GAAG;MAAE,GAAGlC;IAAO,CAAC;;IAE3B;IACA,IAAI,SAAS,IAAIA,MAAM,EAAE;MACvBkC,KAAK,CAAC+F,OAAO,GAAGjI,MAAM,CAACiI,OAAO;MAC9B/F,KAAK,CAACgG,SAAS,GAAGhG,KAAK,CAAC+F,OAAO,GAAI/F,KAAK,CAAC+F,OAAQ;IACnD;IACA,IAAI,aAAa,IAAIjI,MAAM,EAAE;MAC3BkC,KAAK,CAACiG,WAAW,GAAGnI,MAAM,CAACmI,WAAW;MACtCjG,KAAK,CAACkG,aAAa,GAAGlG,KAAK,CAACiG,WAAW,GAAIjG,KAAK,CAACiG,WAAY;IAC/D;IACA,IAAI,SAAS,IAAInI,MAAM,EAAE;MACvBkC,KAAK,CAACmG,OAAO,GAAGrI,MAAM,CAACqI,OAAO;MAC9BnG,KAAK,CAACoG,SAAS,GAAGtI,MAAM,CAACqI,OAAO,GAAIrI,MAAM,CAACqI,OAAQ;IACrD;IACA,IAAI,SAAS,IAAIrI,MAAM,EAAE;MACvBkC,KAAK,CAACH,OAAO,GAAGwG,OAAO,CAACvI,MAAM,CAAC+B,OAAO,CAAC,CACpCyG,GAAG,CAAC,CAAC;QAAEzE;MAAmC,CAAC,KAC1CxE,WAAW,CAACkJ,UAAU,CAAC1E,UAAU,CACnC,CAAC,CACAmD,MAAM,CAAEwB,CAAC,IAAKA,CAAC,CAAC;IACrB,CAAC,MAAM;MACLxG,KAAK,CAACH,OAAO,GAAG,IAAI;IACtB;IACA,IAAI,sBAAsB,IAAI/B,MAAM,EAAE;MACpC,MAAMuF,oBAAoB,GAAG,IAAI,CAACA,oBAAoB,CAAC,IAAI,CAACvF,MAAM,CAAC;MACnEkC,KAAK,CAACsD,oBAAoB,GAAG+C,OAAO,CAACvI,MAAM,CAACwF,oBAAoB,CAAC,CAC9DgD,GAAG,CAAE9C,OAAgC,IAAK;QACzC,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;UAC/B,OAAOnG,WAAW,CAACkJ,UAAU,CAAC/C,OAAO,CAAC;QACxC,CAAC,MAAM;UACL,OAAOnG,WAAW,CAACkJ,UAAU,CAAC/C,OAAO,CAAC3B,UAAU,CAAC;QACnD;MACF,CAAC,CAAC,CACDmD,MAAM,CAAEwB,CAAC,IAAKA,CAAC,CAAC;MAEnB,IAAInD,oBAAoB,KAAK,IAAI,CAACA,oBAAoB,CAACrD,KAAK,CAAC,EAAE;QAC7D1C,gBAAgB,CAAC,MAAM;UACrB;UACA;UACA,IAAI,CAAC6C,OAAO,CAAC,CAAC;UACd,IAAI,CAACsD,OAAO,CAAC,IAAI,CAAC1B,GAAG,EAAE,IAAI,CAACQ,QAAQ,CAAC;QACvC,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACLvC,KAAK,CAACsD,oBAAoB,GAAG,IAAI;IACnC;IAEA,MAAMmD,WAAW,GAAG,CAClB,aAAa,EACb,aAAa,EACb,SAAS,EACT,SAAS,EACT,WAAW,EACX,eAAe,EACf,WAAW,EACX,aAAa,EACb,kBAAkB,EAClB,kBAAkB,EAClB,gBAAgB,EAChB,gBAAgB,EAChB,oBAAoB,EACpB,kBAAkB,EAClB,oBAAoB,EACpB,kBAAkB,CACV;IACVA,WAAW,CAACC,OAAO,CAAEC,IAAkC,IAAK;MAC1D,IAAI,OAAO3G,KAAK,CAAC2G,IAAI,CAAC,KAAK,WAAW,EAAE;QACtC3G,KAAK,CAAC2G,IAAI,CAAC,GAAGC,MAAM,CAACC,GAAG;MAC1B;IACF,CAAC,CAAC;IACF,OAAO7G,KAAK,CAAqB,CAAC;EACpC;AACF;;AAEA;AACA;AACA,SAASyC,oBAAoBA,CAC3BqE,MAGyC,EACzC5F,KAAkB,EAClB;EACA,IAAI4F,MAAM,EAAE;IACV,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;MAChCA,MAAM,CAAC5F,KAAK,CAAC;IACf,CAAC,MAAM;MACL;MACA,IACE,cAAc,IAAI4F,MAAM,IACxB,OAAOA,MAAM,CAACC,YAAY,KAAK,UAAU,EACzC;QACA,MAAMvD,OAAO,GAAGsD,MAAM,CAACC,YAAY,CAAC,CAAC;QACrCtE,oBAAoB,CAACe,OAAO,EAAEtC,KAAK,CAAC;MACtC,CAAC,MAAM;QACL,IAAI,cAAc,IAAI4F,MAAM,EAAE;UAC5B,MAAM;YAAEE;UAAW,CAAC,GAAGF,MAAM,CAACG,YAAY;UAC1C,IAAItH,KAAK,CAACC,OAAO,CAACoH,UAAU,CAAC,EAAE;YAC7B,KAAK,MAAM,CAACE,KAAK,EAAE,CAACC,GAAG,EAAEC,KAAK,CAAC,CAAC,IAAIJ,UAAU,CAACK,OAAO,CAAC,CAAC,EAAE;cACxD,IAAIF,GAAG,IAAIjG,KAAK,CAACS,WAAW,EAAE;gBAC5B;gBACA,MAAM2F,WAAW,GAAGpG,KAAK,CAACS,WAAW,CAACwF,GAAG,CAAC;gBAC1C,IAAIC,KAAK,IAAIA,KAAK,CAACG,QAAQ,EAAE;kBAC3B;kBACAH,KAAK,CAACG,QAAQ,CAACD,WAAW,CAAC;gBAC7B,CAAC,MAAM;kBACL;kBACAR,MAAM,CAACG,YAAY,CAACD,UAAU,CAACE,KAAK,CAAC,GAAG,CAACC,GAAG,EAAEG,WAAW,CAAC;gBAC5D;cACF;YACF;UACF;QACF;MACF;IACF;EACF;AACF;AAEA,SAASjB,OAAOA,CAAIe,KAAc,EAAE;EAClC;EACA,OAAOA,KAAK,IAAI,IAAI,GAAG,EAAE,GAAGzH,KAAK,CAACC,OAAO,CAACwH,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;AACpE;AAEA,eAAe5J,cAAc", "ignoreList": []}