{"version": 3, "names": ["Gesture<PERSON>andler", "IndiscreteGestureHandler", "shouldEnableGestureOnSetup", "updateGestureConfig", "minPointers", "maxPointers", "props", "isGestureEnabledForEvent", "_recognizer", "pointer<PERSON><PERSON><PERSON>", "failed", "validPointerCount", "success"], "sourceRoot": "../../../src", "sources": ["web_hammer/IndiscreteGestureHandler.ts"], "mappings": ";;AAAA,OAAOA,cAAc,MAAM,kBAAkB;;AAE7C;AACA;AACA;AACA,MAAeC,wBAAwB,SAASD,cAAc,CAAC;EAC7D,IAAIE,0BAA0BA,CAAA,EAAG;IAC/B,OAAO,KAAK;EACd;EAEAC,mBAAmBA,CAAC;IAAEC,WAAW,GAAG,CAAC;IAAEC,WAAW,GAAG,CAAC;IAAE,GAAGC;EAAM,CAAC,EAAE;IAClE,OAAO,KAAK,CAACH,mBAAmB,CAAC;MAC/BC,WAAW;MACXC,WAAW;MACX,GAAGC;IACL,CAAC,CAAC;EACJ;EAEAC,wBAAwBA,CACtB;IAAEH,WAAW;IAAEC;EAAiB,CAAC,EACjCG,WAAgB,EAChB;IAAEH,WAAW,EAAEI;EAAmB,CAAC,EACnC;IACA,IAAIA,aAAa,GAAGJ,WAAW,EAAE;MAC/B,OAAO;QAAEK,MAAM,EAAE;MAAK,CAAC;IACzB;IACA,MAAMC,iBAAiB,GAAGF,aAAa,IAAIL,WAAW;IACtD,OAAO;MACLQ,OAAO,EAAED;IACX,CAAC;EACH;AACF;AACA,eAAeV,wBAAwB", "ignoreList": []}