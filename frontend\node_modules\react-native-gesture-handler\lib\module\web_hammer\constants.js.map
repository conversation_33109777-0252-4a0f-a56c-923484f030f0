{"version": 3, "names": ["Hammer", "State", "CONTENT_TOUCHES_DELAY", "CONTENT_TOUCHES_QUICK_TAP_END_DELAY", "MULTI_FINGER_PAN_MAX_PINCH_THRESHOLD", "MULTI_FINGER_PAN_MAX_ROTATION_THRESHOLD", "DEG_RAD", "Math", "PI", "EventMap", "INPUT_START", "BEGAN", "INPUT_MOVE", "ACTIVE", "INPUT_END", "END", "INPUT_CANCEL", "FAILED", "Direction", "RIGHT", "LEFT", "UP", "DOWN", "DirectionMap", "DIRECTION_RIGHT", "DIRECTION_LEFT", "DIRECTION_UP", "DIRECTION_DOWN", "HammerInputNames", "HammerDirectionNames", "DIRECTION_HORIZONTAL", "DIRECTION_VERTICAL", "DIRECTION_NONE", "DIRECTION_ALL"], "sourceRoot": "../../../src", "sources": ["web_hammer/constants.ts"], "mappings": ";;AAAA,OAAOA,MAAM,MAAM,gBAAgB;AAEnC,SAASC,KAAK,QAAQ,UAAU;AAEhC,OAAO,MAAMC,qBAAqB,GAAG,GAAG;AACxC,OAAO,MAAMC,mCAAmC,GAAG,EAAE;AACrD,OAAO,MAAMC,oCAAoC,GAAG,GAAG;AACvD,OAAO,MAAMC,uCAAuC,GAAG,CAAC;AACxD,OAAO,MAAMC,OAAO,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;;AAEpC;AACA,OAAO,MAAMC,QAAQ,GAAG;EACtB,CAACT,MAAM,CAACU,WAAW,GAAGT,KAAK,CAACU,KAAK;EACjC,CAACX,MAAM,CAACY,UAAU,GAAGX,KAAK,CAACY,MAAM;EACjC,CAACb,MAAM,CAACc,SAAS,GAAGb,KAAK,CAACc,GAAG;EAC7B,CAACf,MAAM,CAACgB,YAAY,GAAGf,KAAK,CAACgB;AAC/B,CAAU;AAEV,OAAO,MAAMC,SAAS,GAAG;EACvBC,KAAK,EAAE,CAAC;EACRC,IAAI,EAAE,CAAC;EACPC,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE;AACR,CAAC;AAED,OAAO,MAAMC,YAAY,GAAG;EAC1B,CAACvB,MAAM,CAACwB,eAAe,GAAGN,SAAS,CAACC,KAAK;EACzC,CAACnB,MAAM,CAACyB,cAAc,GAAGP,SAAS,CAACE,IAAI;EACvC,CAACpB,MAAM,CAAC0B,YAAY,GAAGR,SAAS,CAACG,EAAE;EACnC,CAACrB,MAAM,CAAC2B,cAAc,GAAGT,SAAS,CAACI;AACrC,CAAC;AAED,OAAO,MAAMM,gBAAgB,GAAG;EAC9B,CAAC5B,MAAM,CAACU,WAAW,GAAG,OAAO;EAC7B,CAACV,MAAM,CAACY,UAAU,GAAG,MAAM;EAC3B,CAACZ,MAAM,CAACc,SAAS,GAAG,KAAK;EACzB,CAACd,MAAM,CAACgB,YAAY,GAAG;AACzB,CAAC;AACD,OAAO,MAAMa,oBAAoB,GAAG;EAClC,CAAC7B,MAAM,CAAC8B,oBAAoB,GAAG,YAAY;EAC3C,CAAC9B,MAAM,CAAC0B,YAAY,GAAG,IAAI;EAC3B,CAAC1B,MAAM,CAAC2B,cAAc,GAAG,MAAM;EAC/B,CAAC3B,MAAM,CAAC+B,kBAAkB,GAAG,UAAU;EACvC,CAAC/B,MAAM,CAACgC,cAAc,GAAG,MAAM;EAC/B,CAAChC,MAAM,CAACiC,aAAa,GAAG,KAAK;EAC7B,CAACjC,MAAM,CAACwB,eAAe,GAAG,OAAO;EACjC,CAACxB,MAAM,CAACyB,cAAc,GAAG;AAC3B,CAAC", "ignoreList": []}