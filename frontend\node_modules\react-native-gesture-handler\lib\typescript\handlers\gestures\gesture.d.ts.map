{"version": 3, "file": "gesture.d.ts", "sourceRoot": "", "sources": ["../../../../src/handlers/gestures/gesture.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,OAAO,EACP,mBAAmB,EACnB,iBAAiB,EACjB,uBAAuB,EACvB,kBAAkB,EAClB,YAAY,EACZ,WAAW,EACZ,MAAM,yBAAyB,CAAC;AAEjC,OAAO,EAAE,uBAAuB,EAAE,MAAM,uBAAuB,CAAC;AAChE,OAAO,KAAK,EACV,+BAA+B,EAC/B,oCAAoC,EACpC,mCAAmC,EACnC,6BAA6B,EAC7B,+BAA+B,EAC/B,kCAAkC,EAClC,6BAA6B,EAC7B,+BAA+B,EAC/B,+BAA+B,EAChC,MAAM,+BAA+B,CAAC;AAGvC,MAAM,MAAM,WAAW,GACnB,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,GACpC,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,GAClC,WAAW,CAAC,6BAA6B,CAAC,GAC1C,WAAW,CAAC,6BAA6B,CAAC,GAC1C,WAAW,CAAC,mCAAmC,CAAC,GAChD,WAAW,CAAC,kCAAkC,CAAC,GAC/C,WAAW,CAAC,+BAA+B,CAAC,GAC5C,WAAW,CAAC,+BAA+B,CAAC,GAC5C,WAAW,CAAC,oCAAoC,CAAC,GACjD,WAAW,CAAC,+BAA+B,CAAC,GAC5C,WAAW,CAAC,+BAA+B,CAAC,CAAC;AAEjD,MAAM,MAAM,UAAU,GAClB,MAAM,GACN,WAAW,GACX,KAAK,CAAC,SAAS,CAAC,WAAW,GAAG,SAAS,CAAC,GACxC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,aAAa,GAAG,SAAS,CAAC,CAAC;AACrD,MAAM,WAAW,iBACf,SAAQ,mBAAmB,EACzB,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;IACzB,GAAG,CAAC,EAAE,KAAK,CAAC,gBAAgB,CAAC,WAAW,GAAG,SAAS,CAAC,CAAC;IACtD,aAAa,CAAC,EAAE,UAAU,EAAE,CAAC;IAC7B,gBAAgB,CAAC,EAAE,UAAU,EAAE,CAAC;IAChC,cAAc,CAAC,EAAE,UAAU,EAAE,CAAC;IAC9B,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,oBAAoB,CAAC,EAAE,OAAO,CAAC;CAChC;AAED,KAAK,qBAAqB,GAAG,CAC3B,KAAK,EAAE,iBAAiB,EACxB,YAAY,EAAE,uBAAuB,KAClC,IAAI,CAAC;AAEV,MAAM,MAAM,gBAAgB,CAAC,aAAa,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI;IAC5E,SAAS,EAAE,MAAM,CAAC;IAClB,UAAU,EAAE,MAAM,CAAC;IACnB,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,uBAAuB,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC;IAClE,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,uBAAuB,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC;IAClE,KAAK,CAAC,EAAE,CACN,KAAK,EAAE,uBAAuB,CAAC,aAAa,CAAC,EAC7C,OAAO,EAAE,OAAO,KACb,IAAI,CAAC;IACV,UAAU,CAAC,EAAE,CACX,KAAK,EAAE,uBAAuB,CAAC,aAAa,CAAC,EAC7C,OAAO,EAAE,OAAO,KACb,IAAI,CAAC;IACV,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,kBAAkB,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC;IAC9D,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,IAAI,CAAC;IAChC,aAAa,CAAC,EAAE,qBAAqB,CAAC;IACtC,aAAa,CAAC,EAAE,qBAAqB,CAAC;IACtC,WAAW,CAAC,EAAE,qBAAqB,CAAC;IACpC,kBAAkB,CAAC,EAAE,qBAAqB,CAAC;IAC3C,qBAAqB,CAAC,EAAE,CACtB,OAAO,EAAE,kBAAkB,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,EACpD,QAAQ,CAAC,EAAE,kBAAkB,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,KACnD,kBAAkB,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;IACjD,SAAS,EAAE,OAAO,EAAE,CAAC;CACtB,CAAC;AAEF,eAAO,MAAM,aAAa;;;;;;;;;;;;CAYhB,CAAC;AAIX,MAAM,MAAM,aAAa,GAAG,CAAC,OAAO,aAAa,CAAC,CAAC,MAAM,OAAO,aAAa,CAAC,CAAC;AAE/E,8BAAsB,OAAO;IAC3B;;;OAGG;IACH,QAAQ,CAAC,cAAc,IAAI,WAAW,EAAE;IAExC;;OAEG;IACH,QAAQ,CAAC,UAAU,IAAI,IAAI;IAE3B;;;;OAIG;IACH,QAAQ,CAAC,OAAO,IAAI,IAAI;CACzB;AAGD,8BAAsB,WAAW,CAC/B,aAAa,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAC7C,SAAQ,OAAO;IACf,OAAO,CAAC,SAAS,CAAM;IAChB,UAAU,SAAM;IAChB,WAAW,SAAM;IACjB,MAAM,EAAE,iBAAiB,CAAM;IAC/B,QAAQ,EAAE,gBAAgB,CAAC,aAAa,CAAC,CAI9C;;IAcF,OAAO,CAAC,aAAa;IAUrB;;;OAGG;IACH,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,gBAAgB,CAAC,WAAW,GAAG,SAAS,CAAC;IAM5D,SAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,QAAQ;IAKtC;;;;OAIG;IACH,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,uBAAuB,CAAC,aAAa,CAAC,KAAK,IAAI;IAMzE;;;OAGG;IACH,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,uBAAuB,CAAC,aAAa,CAAC,KAAK,IAAI;IAMzE;;;;OAIG;IACH,KAAK,CACH,QAAQ,EAAE,CACR,KAAK,EAAE,uBAAuB,CAAC,aAAa,CAAC,EAC7C,OAAO,EAAE,OAAO,KACb,IAAI;IAQX;;;OAGG;IACH,UAAU,CACR,QAAQ,EAAE,CACR,KAAK,EAAE,uBAAuB,CAAC,aAAa,CAAC,EAC7C,OAAO,EAAE,OAAO,KACb,IAAI;IAQX;;;OAGG;IACH,aAAa,CAAC,QAAQ,EAAE,qBAAqB;IAS7C;;;OAGG;IACH,aAAa,CAAC,QAAQ,EAAE,qBAAqB;IAS7C;;;OAGG;IACH,WAAW,CAAC,QAAQ,EAAE,qBAAqB;IAS3C;;;OAGG;IACH,kBAAkB,CAAC,QAAQ,EAAE,qBAAqB;IASlD;;;;OAIG;IACH,OAAO,CAAC,OAAO,EAAE,OAAO;IAKxB;;;;OAIG;IACH,uBAAuB,CAAC,KAAK,EAAE,OAAO;IAKtC;;;;;OAKG;IACH,OAAO,CAAC,OAAO,EAAE,OAAO;IAKxB;;;;;OAKG;IACH,YAAY,CAAC,YAAY,EAAE,YAAY;IAKvC;;;;;;;OAOG;IACH,WAAW,CAAC,WAAW,EAAE,WAAW;IAKpC;;;;;OAKG;IACH,OAAO,CAAC,OAAO,EAAE,OAAO;IAKxB;;;;OAIG;IACH,+BAA+B,CAAC,GAAG,QAAQ,EAAE,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE;IAO1E;;;;OAIG;IACH,4BAA4B,CAAC,GAAG,QAAQ,EAAE,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE;IAOvE;;;;OAIG;IACH,qBAAqB,CAAC,GAAG,QAAQ,EAAE,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE;IAOhE;;;OAGG;IACH,UAAU,CAAC,EAAE,EAAE,MAAM;IAKrB;;;;;OAKG;IACH,oBAAoB,CAAC,KAAK,EAAE,OAAO;IAKnC,UAAU;IAUV,cAAc,IAAI,WAAW,EAAE;IAK/B,OAAO;IAEP,IAAI,mBAAmB,IAAI,OAAO,CASjC;CACF;AAED,8BAAsB,oBAAoB,CACxC,aAAa,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAC7C,mBAAmB,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CACnD,SAAQ,WAAW,CAAC,aAAa,CAAC;IAClC;;;OAGG;IACH,QAAQ,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,kBAAkB,CAAC,aAAa,CAAC,KAAK,IAAI;IAMrE;;;;OAIG;IACH,QAAQ,CACN,QAAQ,EAAE,CACR,KAAK,EAAE,kBAAkB,CAAC,aAAa,GAAG,mBAAmB,CAAC,KAC3D,IAAI;IAOX;;;;OAIG;IACH,gBAAgB,CAAC,gBAAgB,EAAE,OAAO;CAI3C"}