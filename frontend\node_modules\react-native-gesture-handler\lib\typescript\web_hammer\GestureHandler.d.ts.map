{"version": 3, "file": "GestureHandler.d.ts", "sourceRoot": "", "sources": ["../../../src/web_hammer/GestureHandler.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AAE9C,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACjC,OAAO,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;AAKvC,MAAM,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,EAAE,SAAS,GAAG,SAAS,GAAG,MAAM,CAAC,CAAC;AAE/E,MAAM,MAAM,MAAM,GAAG,OAAO,CAAC;IAC3B,OAAO,EAAE,OAAO,CAAC;IACjB,WAAW,EAAE,MAAM,CAAC;IACpB,WAAW,EAAE,MAAM,CAAC;IACpB,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW,EAAE,MAAM,CAAC;IACpB,aAAa,EAAE,MAAM,CAAC;IACtB,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;IAClB,gBAAgB,EAAE,MAAM,CAAC;IACzB,gBAAgB,EAAE,MAAM,CAAC;IACzB,cAAc,EAAE,MAAM,CAAC;IACvB,cAAc,EAAE,MAAM,CAAC;IACvB,kBAAkB,EAAE,MAAM,CAAC;IAC3B,gBAAgB,EAAE,MAAM,CAAC;IACzB,kBAAkB,EAAE,MAAM,CAAC;IAC3B,gBAAgB,EAAE,MAAM,CAAC;IACzB,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACtB,oBAAoB,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;CACpC,CAAC,CAAC;AAMH,uBAAe,cAAc;IACpB,UAAU,EAAE,GAAG,CAAC;IAChB,gBAAgB,UAAS;IACzB,IAAI,EAAE,MAAM,GAAG,IAAI,CAAQ;IAClC,SAAS,CAAC,2BAA2B,EAAE,OAAO,CAAC;IAC/C,SAAS,CAAC,gBAAgB,UAAS;IACnC,SAAS,CAAC,MAAM,EAAE,aAAa,GAAG,IAAI,CAAQ;IAC9C,SAAS,CAAC,eAAe,EAAE,MAAM,GAAG,IAAI,CAAQ;IAChD,SAAS,CAAC,UAAU,EAAE,GAAG,CAAC;IAC1B,SAAS,CAAC,UAAU,EAAE,GAAG,CAAC;IAC1B,SAAS,CAAC,MAAM,EAAE,MAAM,CAAM;IAC9B,SAAS,CAAC,aAAa,EAAE,KAAK,CAAsB;IACpD,OAAO,CAAC,eAAe,CAA4B;IACnD,OAAO,CAAC,QAAQ,CAA6B;IAC7C,OAAO,CAAC,aAAa,CAAsB;IAC3C,OAAO,CAAC,eAAe,CAAS;IAChC,OAAO,CAAC,aAAa,CAAM;IAC3B,OAAO,CAAC,QAAQ,CAAM;IACtB,OAAO,CAAC,GAAG,CAAM;IAEjB,QAAQ,KAAK,IAAI,IAAI,MAAM,CAAC;IAE5B,IAAI,EAAE,WAEL;IAID,IAAI,QAAQ,YAEX;IAED,IAAI,UAAU,YAEb;IAED,IAAI,0BAA0B,IAAI,OAAO,CAExC;;IAOD,SAAS;iBAtEA,OAAO;qBACH,MAAM;qBACN,MAAM;iBACV,MAAM;mBACJ,MAAM;qBACJ,MAAM;uBACJ,MAAM;iBACZ,MAAM;mBACJ,MAAM;0BACC,MAAM;0BACN,MAAM;wBACR,MAAM;wBACN,MAAM;4BACF,MAAM;0BACR,MAAM;4BACJ,MAAM;0BACR,MAAM;iBACf,GAAG,EAAE,GAAG,IAAI;8BACC,GAAG,EAAE,GAAG,IAAI;;IAwDlC,cAAc,CAAC,QAAQ,EAAE,IAAI;IAE7B,oBAAoB,CAAC,EAAE,EAAE,MAAM;IAI/B,iBAAiB,CAAC,OAAO,EAAE,IAAI;IAI/B,wBAAwB,CACtB,OAAO,EAAE,GAAG,EACZ,WAAW,EAAE,GAAG,EAChB,MAAM,EAAE,GAAG,GACV;QAAE,MAAM,CAAC,EAAE,OAAO,CAAC;QAAC,OAAO,CAAC,EAAE,OAAO,CAAA;KAAE;IAI1C,IAAI,kBAAkB,IAAI,gBAAgB,CAEzC;IAED,iCAAiC,CAAC,OAAO,EAAE,MAAM;IAIjD,kBAAkB,aAMhB;IAEF,mBAAmB,CAAC,EAAE,OAAc,EAAE,GAAG,KAAK,EAAE;;;KAAA;iBA5GvC,OAAO;qBACH,MAAM;qBACN,MAAM;iBACV,MAAM;mBACJ,MAAM;qBACJ,MAAM;uBACJ,MAAM;iBACZ,MAAM;mBACJ,MAAM;0BACC,MAAM;0BACN,MAAM;wBACR,MAAM;wBACN,MAAM;4BACF,MAAM;0BACR,MAAM;4BACJ,MAAM;0BACR,MAAM;iBACf,GAAG,EAAE,GAAG,IAAI;8BACC,GAAG,EAAE,GAAG,IAAI;;IA6GlC,OAAO,aAQL;IAEF,aAAa,GAAI,UAAU;QAAE,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAA;KAAE,aAMjD;IAEF,QAAQ,CAAC,IAAI,EAAE,MAAM,OAAO,QAAQ,GAAG,KAAK;IAQ5C,kBAAkB,CAAC,KAAK,EAAE,cAAc;;;;;;;;;;;IAqCxC,oBAAoB,CAAC,MAAM,EAAE,cAAc;IAI3C,SAAS,GAAI,aAAa,cAAc,UAWtC;IAEF,qBAAqB,CAAC,KAAK,EAAE,cAAc;IAS3C,qBAAqB;IASrB,cAAc,CAAC,KAAK,EAAE,cAAc;IAKpC,eAAe,CAAC,KAAK,EAAE,cAAc;IAOrC,WAAW,CAAC,KAAK,EAAE,cAAc;IAUjC,UAAU,CAAC,EAAE,OAAO,EAAE,EAAE,cAAc;IAMtC,oBAAoB,CAAC,MAAM,EAAE,MAAM;IAMnC,OAAO,CAAC,GAAG,EAAE,UAAU,CAAC,OAAO,cAAc,CAAC,CAAC,GAAG,CAAC,EAAE,QAAQ,EAAE,GAAG;IA6DlE,WAAW;IAkBX,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,cAAc;IAYpD,kBAAkB,CAAC,EAAE,EAAE,cAAc;IAIrC,SAAS;IAET,mBAAmB;IAYnB,eAAe;;;IAUf,IAAI,aA6EF;IAEF,mBAAmB,CAAC,UAAU,EAAE,GAAG;IAGnC,YAAY,CAAC,MAAM,EAAE,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;CA0E/C;AAmDD,eAAe,cAAc,CAAC"}